using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Serilog;

namespace RunMongoDB.Services
{
    /// <summary>
    /// Aggressive MongoDB startup manager with escalating recovery mechanisms
    /// Prioritizes MongoDB startup above all else, including data safety
    /// </summary>
    public class AggressiveStartupManager
    {
        private readonly MongoDBSettings _settings;
        private readonly MongoDBProcessManager _processManager;
        private readonly MongoDBPathConfiguration _pathConfig;
        private int _startupAttempts = 0;
        private readonly List<string> _cleanupActions = new List<string>();

        public AggressiveStartupManager(MongoDBSettings settings, MongoDBProcessManager processManager, MongoDBPathConfiguration pathConfig)
        {
            _settings = settings;
            _processManager = processManager;
            _pathConfig = pathConfig;
            
            Log.Warning("Aggressive Startup Manager initialized - PRIORITIZES STARTUP OVER DATA SAFETY");
        }

        /// <summary>
        /// Attempts to start MongoDB using escalating recovery strategies
        /// </summary>
        public async Task<bool> EnsureMongoDBStartupAsync()
        {
            Log.Information("Starting aggressive MongoDB startup procedure");
            _startupAttempts = 0;
            _cleanupActions.Clear();

            // Check if already running
            if (_processManager.IsMongoDBRunning())
            {
                Log.Information("MongoDB is already running - verifying responsiveness");
                if (await VerifyMongoDBResponsivenessAsync())
                {
                    Log.Information("MongoDB is running and responsive");
                    return true;
                }
                else
                {
                    Log.Warning("MongoDB process exists but not responsive - forcing restart");
                    await ForceStopAllMongoDBProcessesAsync();
                }
            }

            // Execute escalating recovery levels
            var recoveryLevels = new Func<Task<bool>>[]
            {
                () => Level1_StandardStartupAsync(),
                () => Level2_AggressiveCleanupAsync(),
                () => Level3_EmergencyStartupAsync(),
                () => Level4_NuclearOptionAsync()
            };

            foreach (var level in recoveryLevels)
            {
                _startupAttempts++;
                Log.Warning("Attempting startup recovery Level {Level} (Attempt {Attempt})", 
                    _startupAttempts, _startupAttempts);

                try
                {
                    var success = await level();
                    if (success)
                    {
                        Log.Information("MongoDB startup successful at Level {Level}", _startupAttempts);
                        LogCleanupSummary();
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("Level {Level} startup failed: {Error}", _startupAttempts, ex.Message);
                }

                // Wait between attempts
                await Task.Delay(3000);
            }

            Log.Fatal("ALL STARTUP RECOVERY LEVELS FAILED - MongoDB could not be started");
            LogCleanupSummary();
            return false;
        }

        /// <summary>
        /// Level 1: Standard restart with basic cleanup
        /// </summary>
        private async Task<bool> Level1_StandardStartupAsync()
        {
            Log.Information("Level 1: Standard startup with basic cleanup");

            // Basic lock file cleanup
            await CleanupBasicLockFilesAsync();

            // Standard startup attempt
            var started = await _processManager.StartMongoDBAsync();
            if (started)
            {
                return await VerifyMongoDBResponsivenessAsync();
            }

            return false;
        }

        /// <summary>
        /// Level 2: Aggressive file cleanup
        /// </summary>
        private async Task<bool> Level2_AggressiveCleanupAsync()
        {
            Log.Warning("Level 2: Aggressive file cleanup - REMOVING SAFETY FILES");

            // Aggressive cleanup
            await CleanupAllBlockingFilesAsync();
            await CleanupDiagnosticDataAsync();
            await CleanupJournalFilesAsync();
            await CleanupTempFilesAsync();

            // Attempt startup
            var started = await _processManager.StartMongoDBAsync();
            if (started)
            {
                return await VerifyMongoDBResponsivenessAsync();
            }

            return false;
        }

        /// <summary>
        /// Level 3: Emergency startup with minimal configuration
        /// </summary>
        private async Task<bool> Level3_EmergencyStartupAsync()
        {
            Log.Error("Level 3: Emergency startup - BYPASSING SAFETY CHECKS");

            // Complete cleanup
            await CleanupAllBlockingFilesAsync();
            await CleanupDiagnosticDataAsync();
            await CleanupJournalFilesAsync();
            await CleanupTempFilesAsync();
            await CleanupLogFilesAsync();

            // Generate emergency configuration
            var emergencyConfigPath = await GenerateEmergencyConfigAsync();

            // Emergency startup with minimal config
            var started = await StartMongoDBWithEmergencyConfigAsync(emergencyConfigPath);
            if (started)
            {
                return await VerifyMongoDBResponsivenessAsync();
            }

            return false;
        }

        /// <summary>
        /// Level 4: Nuclear option - clear all non-essential data
        /// </summary>
        private async Task<bool> Level4_NuclearOptionAsync()
        {
            Log.Fatal("Level 4: NUCLEAR OPTION - CLEARING NON-ESSENTIAL DATA");
            Log.Fatal("WARNING: THIS MAY CAUSE DATA LOSS");

            // Nuclear cleanup - remove everything that might block startup
            await NuclearCleanupAsync();

            // Create fresh data directory structure
            await InitializeFreshDataDirectoryAsync();

            // Generate minimal emergency configuration
            var emergencyConfigPath = await GenerateMinimalConfigAsync();

            // Final startup attempt
            var started = await StartMongoDBWithEmergencyConfigAsync(emergencyConfigPath);
            if (started)
            {
                return await VerifyMongoDBResponsivenessAsync();
            }

            Log.Fatal("NUCLEAR OPTION FAILED - MongoDB startup impossible");
            return false;
        }

        /// <summary>
        /// Cleans up basic lock files
        /// </summary>
        private async Task CleanupBasicLockFilesAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var lockFile = Path.Combine(_pathConfig.DataDirectory, "mongod.lock");
                    if (File.Exists(lockFile))
                    {
                        File.Delete(lockFile);
                        _cleanupActions.Add($"Removed lock file: {lockFile}");
                        Log.Information("Removed MongoDB lock file: {LockFile}", lockFile);
                    }

                    // Remove PID files
                    var pidFiles = Directory.GetFiles(_pathConfig.DataDirectory, "*.pid", SearchOption.AllDirectories);
                    foreach (var pidFile in pidFiles)
                    {
                        File.Delete(pidFile);
                        _cleanupActions.Add($"Removed PID file: {pidFile}");
                        Log.Information("Removed PID file: {PidFile}", pidFile);
                    }
                }
                catch (Exception ex)
                {
                    Log.Warning("Error during basic cleanup: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Aggressive cleanup of all blocking files
        /// </summary>
        private async Task CleanupAllBlockingFilesAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var dataDir = _pathConfig.DataDirectory;
                    
                    // Remove all lock files
                    var lockFiles = Directory.GetFiles(dataDir, "*.lock", SearchOption.AllDirectories);
                    foreach (var file in lockFiles)
                    {
                        try
                        {
                            File.Delete(file);
                            _cleanupActions.Add($"Removed lock file: {file}");
                            Log.Warning("Aggressively removed lock file: {File}", file);
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Could not remove lock file {File}: {Error}", file, ex.Message);
                        }
                    }

                    // Remove PID files
                    var pidFiles = Directory.GetFiles(dataDir, "*.pid", SearchOption.AllDirectories);
                    foreach (var file in pidFiles)
                    {
                        try
                        {
                            File.Delete(file);
                            _cleanupActions.Add($"Removed PID file: {file}");
                            Log.Warning("Removed PID file: {File}", file);
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Could not remove PID file {File}: {Error}", file, ex.Message);
                        }
                    }

                    // Remove socket files (Unix-style)
                    var socketFiles = Directory.GetFiles(dataDir, "*.sock", SearchOption.AllDirectories);
                    foreach (var file in socketFiles)
                    {
                        try
                        {
                            File.Delete(file);
                            _cleanupActions.Add($"Removed socket file: {file}");
                            Log.Warning("Removed socket file: {File}", file);
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Could not remove socket file {File}: {Error}", file, ex.Message);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("Error during aggressive file cleanup: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Cleanup diagnostic data directory
        /// </summary>
        private async Task CleanupDiagnosticDataAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var diagnosticPath = Path.Combine(_pathConfig.DataDirectory, "diagnostic.data");
                    if (Directory.Exists(diagnosticPath))
                    {
                        var files = Directory.GetFiles(diagnosticPath, "*", SearchOption.AllDirectories);
                        foreach (var file in files)
                        {
                            try
                            {
                                File.Delete(file);
                                _cleanupActions.Add($"Removed diagnostic file: {file}");
                            }
                            catch (Exception ex)
                            {
                                Log.Warning("Could not remove diagnostic file {File}: {Error}", file, ex.Message);
                            }
                        }
                        
                        // Try to remove the directory itself
                        try
                        {
                            Directory.Delete(diagnosticPath, true);
                            _cleanupActions.Add($"Removed diagnostic directory: {diagnosticPath}");
                            Log.Warning("Removed entire diagnostic.data directory: {Path}", diagnosticPath);
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Could not remove diagnostic directory: {Error}", ex.Message);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("Error cleaning diagnostic data: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Cleanup journal files (WARNING: May cause data loss)
        /// </summary>
        private async Task CleanupJournalFilesAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    Log.Fatal("WARNING: Removing journal files - THIS MAY CAUSE DATA LOSS");
                    
                    var journalPath = Path.Combine(_pathConfig.DataDirectory, "journal");
                    if (Directory.Exists(journalPath))
                    {
                        var files = Directory.GetFiles(journalPath, "*", SearchOption.AllDirectories);
                        foreach (var file in files)
                        {
                            try
                            {
                                File.Delete(file);
                                _cleanupActions.Add($"REMOVED JOURNAL FILE: {file}");
                                Log.Fatal("REMOVED JOURNAL FILE (DATA LOSS RISK): {File}", file);
                            }
                            catch (Exception ex)
                            {
                                Log.Warning("Could not remove journal file {File}: {Error}", file, ex.Message);
                            }
                        }
                        
                        try
                        {
                            Directory.Delete(journalPath, true);
                            _cleanupActions.Add($"REMOVED JOURNAL DIRECTORY: {journalPath}");
                            Log.Fatal("REMOVED ENTIRE JOURNAL DIRECTORY: {Path}", journalPath);
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Could not remove journal directory: {Error}", ex.Message);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("Error cleaning journal files: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Cleanup temporary files
        /// </summary>
        private async Task CleanupTempFilesAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var dataDir = _pathConfig.DataDirectory;
                    
                    // Remove temp files
                    var tempFiles = Directory.GetFiles(dataDir, "*.tmp", SearchOption.AllDirectories)
                        .Concat(Directory.GetFiles(dataDir, "tmp*", SearchOption.AllDirectories))
                        .Concat(Directory.GetFiles(dataDir, "*.temp", SearchOption.AllDirectories));
                    
                    foreach (var file in tempFiles)
                    {
                        try
                        {
                            File.Delete(file);
                            _cleanupActions.Add($"Removed temp file: {file}");
                            Log.Information("Removed temp file: {File}", file);
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Could not remove temp file {File}: {Error}", file, ex.Message);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("Error cleaning temp files: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Cleanup log files that might be locked
        /// </summary>
        private async Task CleanupLogFilesAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var logDir = _pathConfig.LogDirectory;
                    var mongodLogFile = Path.Combine(logDir, "mongod.log");
                    
                    if (File.Exists(mongodLogFile))
                    {
                        try
                        {
                            // Try to rename first (in case it's locked)
                            var backupName = $"mongod_backup_{DateTime.Now:yyyyMMdd_HHmmss}.log";
                            var backupPath = Path.Combine(logDir, backupName);
                            File.Move(mongodLogFile, backupPath);
                            _cleanupActions.Add($"Moved log file: {mongodLogFile} -> {backupPath}");
                            Log.Warning("Moved potentially locked log file: {File}", mongodLogFile);
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Could not move log file: {Error}", ex.Message);
                            
                            // Try to delete if move failed
                            try
                            {
                                File.Delete(mongodLogFile);
                                _cleanupActions.Add($"Deleted log file: {mongodLogFile}");
                                Log.Warning("Deleted locked log file: {File}", mongodLogFile);
                            }
                            catch (Exception deleteEx)
                            {
                                Log.Warning("Could not delete log file: {Error}", deleteEx.Message);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("Error cleaning log files: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Nuclear cleanup - removes all non-essential files (WARNING: DATA LOSS)
        /// </summary>
        private async Task NuclearCleanupAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    Log.Fatal("EXECUTING NUCLEAR CLEANUP - THIS WILL CAUSE DATA LOSS");

                    var dataDir = _pathConfig.DataDirectory;

                    // List of patterns to preserve (essential database files)
                    var preservePatterns = new[] { "*.bson", "*.wt", "WiredTiger*" };
                    var preserveFiles = new HashSet<string>();

                    foreach (var pattern in preservePatterns)
                    {
                        var files = Directory.GetFiles(dataDir, pattern, SearchOption.AllDirectories);
                        foreach (var file in files)
                        {
                            preserveFiles.Add(file.ToLowerInvariant());
                        }
                    }

                    // Remove everything else
                    var allFiles = Directory.GetFiles(dataDir, "*", SearchOption.AllDirectories);
                    foreach (var file in allFiles)
                    {
                        if (!preserveFiles.Contains(file.ToLowerInvariant()))
                        {
                            try
                            {
                                File.Delete(file);
                                _cleanupActions.Add($"NUCLEAR: Removed {file}");
                                Log.Fatal("NUCLEAR CLEANUP: Removed {File}", file);
                            }
                            catch (Exception ex)
                            {
                                Log.Warning("Could not remove file {File}: {Error}", file, ex.Message);
                            }
                        }
                    }

                    // Remove empty directories
                    var directories = Directory.GetDirectories(dataDir, "*", SearchOption.AllDirectories)
                        .OrderByDescending(d => d.Length); // Remove deepest first

                    foreach (var dir in directories)
                    {
                        try
                        {
                            if (!Directory.EnumerateFileSystemEntries(dir).Any())
                            {
                                Directory.Delete(dir);
                                _cleanupActions.Add($"NUCLEAR: Removed empty directory {dir}");
                                Log.Fatal("NUCLEAR CLEANUP: Removed empty directory {Dir}", dir);
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Could not remove directory {Dir}: {Error}", dir, ex.Message);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("Error during nuclear cleanup: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Initialize fresh data directory structure
        /// </summary>
        private async Task InitializeFreshDataDirectoryAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    Log.Warning("Initializing fresh data directory structure");

                    // Ensure data directory exists
                    Directory.CreateDirectory(_pathConfig.DataDirectory);

                    // Create essential subdirectories
                    var essentialDirs = new[] { "journal", "diagnostic.data" };
                    foreach (var dir in essentialDirs)
                    {
                        var fullPath = Path.Combine(_pathConfig.DataDirectory, dir);
                        Directory.CreateDirectory(fullPath);
                        _cleanupActions.Add($"Created directory: {fullPath}");
                        Log.Information("Created essential directory: {Dir}", fullPath);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("Error initializing fresh data directory: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Generate emergency MongoDB configuration
        /// </summary>
        private async Task<string> GenerateEmergencyConfigAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    var configPath = Path.Combine(_pathConfig.DataDirectory, "emergency_mongod.cfg");

                    var config = $@"# EMERGENCY MongoDB Configuration - MINIMAL SAFETY
# Generated by Aggressive Startup Manager
# WARNING: This configuration prioritizes startup over data safety

storage:
  dbPath: ""{_pathConfig.DataDirectory.Replace("\\", "/")}""
  journal:
    enabled: false  # DISABLED for emergency startup
  engine: wiredTiger
  wiredTiger:
    engineConfig:
      cacheSizeGB: 0.5  # Minimal cache
      statisticsLogDelaySecs: 0
    collectionConfig:
      blockCompressor: none
    indexConfig:
      prefixCompression: false

net:
  port: {_settings.Port}
  bindIp: {_settings.Host}
  maxIncomingConnections: 50  # Minimal connections

systemLog:
  destination: file
  path: ""{Path.Combine(_pathConfig.LogDirectory, "emergency_mongod.log").Replace("\\", "/")}""
  logAppend: false  # Overwrite log
  verbosity: 0
  quiet: true

processManagement:
  fork: false

operationProfiling:
  mode: off

security:
  authorization: disabled  # DISABLED for emergency startup

setParameter:
  enableSchemaValidation: false
  writeConcernMajorityJournalDefault: false
  enableTestCommands: false
  failIndexKeyTooLong: false  # Allow startup with problematic indexes
";

                    File.WriteAllText(configPath, config);
                    _cleanupActions.Add($"Generated emergency config: {configPath}");
                    Log.Warning("Generated emergency MongoDB configuration: {ConfigPath}", configPath);

                    return configPath;
                }
                catch (Exception ex)
                {
                    Log.Error("Error generating emergency config: {Error}", ex.Message);
                    return null;
                }
            });
        }

        /// <summary>
        /// Generate minimal MongoDB configuration for nuclear option
        /// </summary>
        private async Task<string> GenerateMinimalConfigAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    var configPath = Path.Combine(_pathConfig.DataDirectory, "minimal_mongod.cfg");

                    var config = $@"# MINIMAL MongoDB Configuration - NUCLEAR OPTION
# Generated by Aggressive Startup Manager
# WARNING: MAXIMUM PERFORMANCE, MINIMUM SAFETY

storage:
  dbPath: ""{_pathConfig.DataDirectory.Replace("\\", "/")}""
  journal:
    enabled: false  # DISABLED
  engine: wiredTiger
  wiredTiger:
    engineConfig:
      cacheSizeGB: 0.25  # Absolute minimum
      statisticsLogDelaySecs: 0
      directoryForIndexes: false
    collectionConfig:
      blockCompressor: none
    indexConfig:
      prefixCompression: false

net:
  port: {_settings.Port}
  bindIp: 127.0.0.1
  maxIncomingConnections: 20  # Absolute minimum
  wireObjectCheck: false

systemLog:
  destination: file
  path: ""{Path.Combine(_pathConfig.LogDirectory, "minimal_mongod.log").Replace("\\", "/")}""
  logAppend: false
  verbosity: 0
  quiet: true

processManagement:
  fork: false

operationProfiling:
  mode: off

security:
  authorization: disabled

setParameter:
  enableSchemaValidation: false
  writeConcernMajorityJournalDefault: false
  enableTestCommands: false
  failIndexKeyTooLong: false
  enableLocalhostAuthBypass: true
  skipShardingConfigurationChecks: true
";

                    File.WriteAllText(configPath, config);
                    _cleanupActions.Add($"Generated minimal config: {configPath}");
                    Log.Fatal("Generated MINIMAL MongoDB configuration: {ConfigPath}", configPath);

                    return configPath;
                }
                catch (Exception ex)
                {
                    Log.Error("Error generating minimal config: {Error}", ex.Message);
                    return null;
                }
            });
        }

        /// <summary>
        /// Start MongoDB with emergency configuration
        /// </summary>
        private async Task<bool> StartMongoDBWithEmergencyConfigAsync(string configPath)
        {
            try
            {
                if (string.IsNullOrEmpty(configPath) || !File.Exists(configPath))
                {
                    Log.Error("Emergency config file not available: {ConfigPath}", configPath);
                    return false;
                }

                Log.Warning("Starting MongoDB with emergency configuration: {ConfigPath}", configPath);

                var startInfo = new ProcessStartInfo
                {
                    FileName = _pathConfig.MongoDBExecutablePath,
                    WorkingDirectory = Path.GetDirectoryName(_pathConfig.MongoDBExecutablePath),
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                startInfo.ArgumentList.Add("--config");
                startInfo.ArgumentList.Add(configPath);

                // Add emergency startup flags
                startInfo.ArgumentList.Add("--nojournal");  // Force disable journaling
                startInfo.ArgumentList.Add("--smallfiles");  // Use small files for faster startup
                startInfo.ArgumentList.Add("--noprealloc");  // Don't preallocate files

                var process = Process.Start(startInfo);
                if (process != null)
                {
                    Log.Warning("Emergency MongoDB process started with PID: {ProcessId}", process.Id);

                    // Wait longer for emergency startup
                    await Task.Delay(10000);

                    if (!process.HasExited)
                    {
                        _cleanupActions.Add($"Emergency startup successful with PID: {process.Id}");
                        Log.Warning("Emergency MongoDB startup successful");
                        return true;
                    }
                    else
                    {
                        Log.Error("Emergency MongoDB process exited immediately with code: {ExitCode}", process.ExitCode);
                        return false;
                    }
                }
                else
                {
                    Log.Error("Failed to start emergency MongoDB process");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error starting MongoDB with emergency config: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Force stop all MongoDB processes
        /// </summary>
        private async Task ForceStopAllMongoDBProcessesAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    Log.Warning("Force stopping all MongoDB processes");

                    var processes = Process.GetProcessesByName("mongod");
                    foreach (var process in processes)
                    {
                        try
                        {
                            Log.Warning("Force killing MongoDB process {ProcessId}", process.Id);
                            process.Kill(true);
                            _cleanupActions.Add($"Force killed process: {process.Id}");
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Could not kill process {ProcessId}: {Error}", process.Id, ex.Message);
                        }
                        finally
                        {
                            process.Dispose();
                        }
                    }

                    // Wait for processes to die
                    Task.Delay(3000).Wait();
                }
                catch (Exception ex)
                {
                    Log.Error("Error force stopping MongoDB processes: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Verify MongoDB is actually responding, not just running
        /// </summary>
        private async Task<bool> VerifyMongoDBResponsivenessAsync()
        {
            try
            {
                Log.Information("Verifying MongoDB responsiveness");

                // Wait a moment for MongoDB to initialize
                await Task.Delay(5000);

                // Check if process is still running
                if (!_processManager.IsMongoDBRunning())
                {
                    Log.Warning("MongoDB process not running during verification");
                    return false;
                }

                // Try to connect and ping
                var connectionString = $"mongodb://{_settings.Host}:{_settings.Port}";
                var client = new MongoDB.Driver.MongoClient(connectionString);

                // Set a short timeout for verification
                var database = client.GetDatabase("admin");
                var command = new MongoDB.Bson.BsonDocument("ping", 1);

                // Use a timeout for the ping
                var pingTask = database.RunCommandAsync<MongoDB.Bson.BsonDocument>(command);
                var timeoutTask = Task.Delay(10000); // 10 second timeout

                var completedTask = await Task.WhenAny(pingTask, timeoutTask);

                if (completedTask == pingTask)
                {
                    var result = await pingTask;
                    if (result != null && result.Contains("ok") && result["ok"].ToDouble() == 1.0)
                    {
                        Log.Information("MongoDB responsiveness verification PASSED");
                        return true;
                    }
                }

                Log.Warning("MongoDB responsiveness verification FAILED - no response to ping");
                return false;
            }
            catch (Exception ex)
            {
                Log.Warning("MongoDB responsiveness verification FAILED: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Log summary of all cleanup actions taken
        /// </summary>
        private void LogCleanupSummary()
        {
            Log.Information("=== AGGRESSIVE STARTUP CLEANUP SUMMARY ===");
            Log.Information("Total startup attempts: {Attempts}", _startupAttempts);
            Log.Information("Total cleanup actions: {Actions}", _cleanupActions.Count);

            if (_cleanupActions.Count > 0)
            {
                Log.Information("Cleanup actions performed:");
                for (int i = 0; i < _cleanupActions.Count; i++)
                {
                    Log.Information("  {Index}: {Action}", i + 1, _cleanupActions[i]);
                }
            }

            Log.Information("=== END CLEANUP SUMMARY ===");
        }

        public int StartupAttempts => _startupAttempts;
        public IReadOnlyList<string> CleanupActions => _cleanupActions.AsReadOnly();
    }
}
