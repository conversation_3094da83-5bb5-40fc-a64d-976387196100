using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Serilog;

namespace RunMongoDB.Services
{
    /// <summary>
    /// Utility for testing MongoDB startup reliability and aggressive recovery mechanisms
    /// </summary>
    public class StartupReliabilityTester
    {
        private readonly MongoDBSettings _settings;
        private readonly AggressiveStartupManager _aggressiveStartup;
        private readonly MongoDBPathConfiguration _pathConfig;

        public StartupReliabilityTester(MongoDBSettings settings, AggressiveStartupManager aggressiveStartup, MongoDBPathConfiguration pathConfig)
        {
            _settings = settings;
            _aggressiveStartup = aggressiveStartup;
            _pathConfig = pathConfig;
        }

        /// <summary>
        /// Runs comprehensive startup reliability tests
        /// </summary>
        public async Task<StartupTestResults> RunStartupReliabilityTestsAsync()
        {
            var results = new StartupTestResults
            {
                TestStartTime = DateTime.Now
            };

            Log.Information("=== Starting MongoDB Startup Reliability Tests ===");

            try
            {
                // Test 1: Normal startup
                results.NormalStartupTest = await TestNormalStartupAsync();

                // Test 2: Startup with lock files
                results.LockFileRecoveryTest = await TestLockFileRecoveryAsync();

                // Test 3: Startup with corrupted diagnostic data
                results.DiagnosticDataRecoveryTest = await TestDiagnosticDataRecoveryAsync();

                // Test 4: Startup with journal corruption
                results.JournalCorruptionRecoveryTest = await TestJournalCorruptionRecoveryAsync();

                // Test 5: Emergency startup test
                results.EmergencyStartupTest = await TestEmergencyStartupAsync();

                // Test 6: Nuclear option test (WARNING: DATA LOSS)
                if (ConfirmNuclearTest())
                {
                    results.NuclearOptionTest = await TestNuclearOptionAsync();
                }

                results.TestEndTime = DateTime.Now;
                results.TotalTestDuration = results.TestEndTime - results.TestStartTime;
                results.OverallSuccess = CalculateOverallSuccess(results);

                LogTestSummary(results);
                return results;
            }
            catch (Exception ex)
            {
                Log.Error("Error during startup reliability tests: {Error}", ex.Message);
                results.TestEndTime = DateTime.Now;
                results.TotalTestDuration = results.TestEndTime - results.TestStartTime;
                results.OverallSuccess = false;
                return results;
            }
        }

        /// <summary>
        /// Test normal startup without any blocking conditions
        /// </summary>
        private async Task<TestResult> TestNormalStartupAsync()
        {
            Log.Information("Test 1: Normal startup test");
            var startTime = DateTime.Now;

            try
            {
                // Ensure clean state
                await StopMongoDBIfRunningAsync();
                await CleanupTestFilesAsync();

                // Test normal startup
                var success = await _aggressiveStartup.EnsureMongoDBStartupAsync();
                var endTime = DateTime.Now;

                return new TestResult
                {
                    TestName = "Normal Startup",
                    Success = success,
                    Duration = endTime - startTime,
                    StartupAttempts = _aggressiveStartup.StartupAttempts,
                    CleanupActions = _aggressiveStartup.CleanupActions.Count,
                    Details = success ? "Normal startup successful" : "Normal startup failed"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "Normal Startup",
                    Success = false,
                    Duration = DateTime.Now - startTime,
                    Details = $"Test failed: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Test startup recovery when lock files are present
        /// </summary>
        private async Task<TestResult> TestLockFileRecoveryAsync()
        {
            Log.Information("Test 2: Lock file recovery test");
            var startTime = DateTime.Now;

            try
            {
                // Ensure clean state
                await StopMongoDBIfRunningAsync();
                await CleanupTestFilesAsync();

                // Create blocking lock files
                await CreateTestLockFilesAsync();

                // Test startup with lock files
                var success = await _aggressiveStartup.EnsureMongoDBStartupAsync();
                var endTime = DateTime.Now;

                return new TestResult
                {
                    TestName = "Lock File Recovery",
                    Success = success,
                    Duration = endTime - startTime,
                    StartupAttempts = _aggressiveStartup.StartupAttempts,
                    CleanupActions = _aggressiveStartup.CleanupActions.Count,
                    Details = success ? "Lock file recovery successful" : "Lock file recovery failed"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "Lock File Recovery",
                    Success = false,
                    Duration = DateTime.Now - startTime,
                    Details = $"Test failed: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Test startup recovery when diagnostic data is corrupted
        /// </summary>
        private async Task<TestResult> TestDiagnosticDataRecoveryAsync()
        {
            Log.Information("Test 3: Diagnostic data recovery test");
            var startTime = DateTime.Now;

            try
            {
                // Ensure clean state
                await StopMongoDBIfRunningAsync();
                await CleanupTestFilesAsync();

                // Create corrupted diagnostic data
                await CreateCorruptedDiagnosticDataAsync();

                // Test startup with corrupted diagnostic data
                var success = await _aggressiveStartup.EnsureMongoDBStartupAsync();
                var endTime = DateTime.Now;

                return new TestResult
                {
                    TestName = "Diagnostic Data Recovery",
                    Success = success,
                    Duration = endTime - startTime,
                    StartupAttempts = _aggressiveStartup.StartupAttempts,
                    CleanupActions = _aggressiveStartup.CleanupActions.Count,
                    Details = success ? "Diagnostic data recovery successful" : "Diagnostic data recovery failed"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "Diagnostic Data Recovery",
                    Success = false,
                    Duration = DateTime.Now - startTime,
                    Details = $"Test failed: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Test startup recovery when journal files are corrupted
        /// </summary>
        private async Task<TestResult> TestJournalCorruptionRecoveryAsync()
        {
            Log.Information("Test 4: Journal corruption recovery test");
            var startTime = DateTime.Now;

            try
            {
                // Ensure clean state
                await StopMongoDBIfRunningAsync();
                await CleanupTestFilesAsync();

                // Create corrupted journal files
                await CreateCorruptedJournalFilesAsync();

                // Test startup with corrupted journal
                var success = await _aggressiveStartup.EnsureMongoDBStartupAsync();
                var endTime = DateTime.Now;

                return new TestResult
                {
                    TestName = "Journal Corruption Recovery",
                    Success = success,
                    Duration = endTime - startTime,
                    StartupAttempts = _aggressiveStartup.StartupAttempts,
                    CleanupActions = _aggressiveStartup.CleanupActions.Count,
                    Details = success ? "Journal corruption recovery successful" : "Journal corruption recovery failed"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "Journal Corruption Recovery",
                    Success = false,
                    Duration = DateTime.Now - startTime,
                    Details = $"Test failed: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Test emergency startup procedures
        /// </summary>
        private async Task<TestResult> TestEmergencyStartupAsync()
        {
            Log.Information("Test 5: Emergency startup test");
            var startTime = DateTime.Now;

            try
            {
                // Ensure clean state
                await StopMongoDBIfRunningAsync();
                await CleanupTestFilesAsync();

                // Create multiple blocking conditions
                await CreateTestLockFilesAsync();
                await CreateCorruptedDiagnosticDataAsync();
                await CreateCorruptedJournalFilesAsync();

                // Test emergency startup
                var success = await _aggressiveStartup.EnsureMongoDBStartupAsync();
                var endTime = DateTime.Now;

                return new TestResult
                {
                    TestName = "Emergency Startup",
                    Success = success,
                    Duration = endTime - startTime,
                    StartupAttempts = _aggressiveStartup.StartupAttempts,
                    CleanupActions = _aggressiveStartup.CleanupActions.Count,
                    Details = success ? "Emergency startup successful" : "Emergency startup failed"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "Emergency Startup",
                    Success = false,
                    Duration = DateTime.Now - startTime,
                    Details = $"Test failed: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Test nuclear option (WARNING: DATA LOSS)
        /// </summary>
        private async Task<TestResult> TestNuclearOptionAsync()
        {
            Log.Fatal("Test 6: NUCLEAR OPTION TEST - THIS WILL CAUSE DATA LOSS");
            var startTime = DateTime.Now;

            try
            {
                // Create maximum blocking conditions
                await StopMongoDBIfRunningAsync();
                await CreateMaximumBlockingConditionsAsync();

                // Test nuclear option
                var success = await _aggressiveStartup.EnsureMongoDBStartupAsync();
                var endTime = DateTime.Now;

                return new TestResult
                {
                    TestName = "Nuclear Option",
                    Success = success,
                    Duration = endTime - startTime,
                    StartupAttempts = _aggressiveStartup.StartupAttempts,
                    CleanupActions = _aggressiveStartup.CleanupActions.Count,
                    Details = success ? "Nuclear option successful" : "Nuclear option failed"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "Nuclear Option",
                    Success = false,
                    Duration = DateTime.Now - startTime,
                    Details = $"Test failed: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Stop MongoDB if running
        /// </summary>
        private async Task StopMongoDBIfRunningAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var processes = Process.GetProcessesByName("mongod");
                    foreach (var process in processes)
                    {
                        try
                        {
                            process.Kill(true);
                            Log.Information("Stopped MongoDB process {ProcessId} for testing", process.Id);
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Could not stop process {ProcessId}: {Error}", process.Id, ex.Message);
                        }
                        finally
                        {
                            process.Dispose();
                        }
                    }
                    
                    // Wait for processes to stop
                    Task.Delay(3000).Wait();
                }
                catch (Exception ex)
                {
                    Log.Warning("Error stopping MongoDB for testing: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Clean up test files
        /// </summary>
        private async Task CleanupTestFilesAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var dataDir = _pathConfig.DataDirectory;
                    
                    // Remove test files
                    var testFiles = new[]
                    {
                        Path.Combine(dataDir, "mongod.lock"),
                        Path.Combine(dataDir, "test.pid"),
                        Path.Combine(dataDir, "test.sock")
                    };
                    
                    foreach (var file in testFiles)
                    {
                        if (File.Exists(file))
                        {
                            File.Delete(file);
                        }
                    }
                    
                    // Remove test directories
                    var testDirs = new[]
                    {
                        Path.Combine(dataDir, "diagnostic.data"),
                        Path.Combine(dataDir, "journal")
                    };
                    
                    foreach (var dir in testDirs)
                    {
                        if (Directory.Exists(dir))
                        {
                            Directory.Delete(dir, true);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Debug("Error cleaning test files: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Create test lock files
        /// </summary>
        private async Task CreateTestLockFilesAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var dataDir = _pathConfig.DataDirectory;
                    Directory.CreateDirectory(dataDir);
                    
                    // Create lock file
                    var lockFile = Path.Combine(dataDir, "mongod.lock");
                    File.WriteAllText(lockFile, "12345");
                    
                    // Create PID file
                    var pidFile = Path.Combine(dataDir, "test.pid");
                    File.WriteAllText(pidFile, "67890");
                    
                    Log.Information("Created test lock files for testing");
                }
                catch (Exception ex)
                {
                    Log.Warning("Error creating test lock files: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Create corrupted diagnostic data
        /// </summary>
        private async Task CreateCorruptedDiagnosticDataAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var diagnosticDir = Path.Combine(_pathConfig.DataDirectory, "diagnostic.data");
                    Directory.CreateDirectory(diagnosticDir);
                    
                    // Create corrupted diagnostic files
                    var corruptedFile = Path.Combine(diagnosticDir, "metrics.interim");
                    File.WriteAllText(corruptedFile, "CORRUPTED DATA FOR TESTING");
                    
                    Log.Information("Created corrupted diagnostic data for testing");
                }
                catch (Exception ex)
                {
                    Log.Warning("Error creating corrupted diagnostic data: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Create corrupted journal files
        /// </summary>
        private async Task CreateCorruptedJournalFilesAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var journalDir = Path.Combine(_pathConfig.DataDirectory, "journal");
                    Directory.CreateDirectory(journalDir);
                    
                    // Create corrupted journal file
                    var journalFile = Path.Combine(journalDir, "j._0");
                    File.WriteAllText(journalFile, "CORRUPTED JOURNAL DATA FOR TESTING");
                    
                    Log.Information("Created corrupted journal files for testing");
                }
                catch (Exception ex)
                {
                    Log.Warning("Error creating corrupted journal files: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Create maximum blocking conditions for nuclear test
        /// </summary>
        private async Task CreateMaximumBlockingConditionsAsync()
        {
            await CreateTestLockFilesAsync();
            await CreateCorruptedDiagnosticDataAsync();
            await CreateCorruptedJournalFilesAsync();
            
            await Task.Run(() =>
            {
                try
                {
                    var dataDir = _pathConfig.DataDirectory;
                    
                    // Create additional blocking files
                    File.WriteAllText(Path.Combine(dataDir, "storage.bson"), "CORRUPTED");
                    File.WriteAllText(Path.Combine(dataDir, "WiredTiger"), "CORRUPTED");
                    File.WriteAllText(Path.Combine(dataDir, "WiredTiger.lock"), "LOCKED");
                    
                    Log.Fatal("Created maximum blocking conditions for nuclear test");
                }
                catch (Exception ex)
                {
                    Log.Warning("Error creating maximum blocking conditions: {Error}", ex.Message);
                }
            });
        }

        /// <summary>
        /// Confirm nuclear test execution
        /// </summary>
        private bool ConfirmNuclearTest()
        {
            Log.Warning("Nuclear option test will cause DATA LOSS. Skipping for safety.");
            return false; // Skip nuclear test by default for safety
        }

        /// <summary>
        /// Calculate overall test success
        /// </summary>
        private bool CalculateOverallSuccess(StartupTestResults results)
        {
            return results.NormalStartupTest.Success &&
                   results.LockFileRecoveryTest.Success &&
                   results.DiagnosticDataRecoveryTest.Success &&
                   results.JournalCorruptionRecoveryTest.Success &&
                   results.EmergencyStartupTest.Success;
        }

        /// <summary>
        /// Log test summary
        /// </summary>
        private void LogTestSummary(StartupTestResults results)
        {
            Log.Information("=== STARTUP RELIABILITY TEST SUMMARY ===");
            Log.Information("Total test duration: {Duration}", results.TotalTestDuration);
            Log.Information("Overall success: {Success}", results.OverallSuccess);
            Log.Information("");
            
            LogTestResult(results.NormalStartupTest);
            LogTestResult(results.LockFileRecoveryTest);
            LogTestResult(results.DiagnosticDataRecoveryTest);
            LogTestResult(results.JournalCorruptionRecoveryTest);
            LogTestResult(results.EmergencyStartupTest);
            
            if (results.NuclearOptionTest != null)
            {
                LogTestResult(results.NuclearOptionTest);
            }
            
            Log.Information("=== END TEST SUMMARY ===");
        }

        /// <summary>
        /// Log individual test result
        /// </summary>
        private void LogTestResult(TestResult result)
        {
            var status = result.Success ? "PASS" : "FAIL";
            Log.Information("{TestName}: {Status} ({Duration}s, {Attempts} attempts, {Actions} cleanup actions)",
                result.TestName, status, result.Duration.TotalSeconds, result.StartupAttempts, result.CleanupActions);
            
            if (!string.IsNullOrEmpty(result.Details))
            {
                Log.Information("  Details: {Details}", result.Details);
            }
        }
    }

    /// <summary>
    /// Startup test results
    /// </summary>
    public class StartupTestResults
    {
        public DateTime TestStartTime { get; set; }
        public DateTime TestEndTime { get; set; }
        public TimeSpan TotalTestDuration { get; set; }
        public bool OverallSuccess { get; set; }
        
        public TestResult NormalStartupTest { get; set; }
        public TestResult LockFileRecoveryTest { get; set; }
        public TestResult DiagnosticDataRecoveryTest { get; set; }
        public TestResult JournalCorruptionRecoveryTest { get; set; }
        public TestResult EmergencyStartupTest { get; set; }
        public TestResult NuclearOptionTest { get; set; }
    }

    /// <summary>
    /// Individual test result
    /// </summary>
    public class TestResult
    {
        public string TestName { get; set; }
        public bool Success { get; set; }
        public TimeSpan Duration { get; set; }
        public int StartupAttempts { get; set; }
        public int CleanupActions { get; set; }
        public string Details { get; set; }
    }
}
