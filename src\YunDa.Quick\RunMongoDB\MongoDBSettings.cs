﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RunMongoDB
{
    public class MongoDBSettings
    {
        public string Host { get; set; } = "127.0.0.1";
        public string Port { get; set; } = "37017";
        public string DatabaseName { get; set; } = "isas_mongodb";
        public string IsAuth { get; set; } = "false";
        public string UserName { get; set; } = "isasAdmin";
        public string PassWord { get; set; } = "123456";

        // Enhanced configuration properties
        public string MongoDBExecutablePath { get; set; } = @"D:\SOMS\大数据库\mongod.exe";
        public string MongoDBConfigPath { get; set; } = @"D:\SOMS\大数据库\mongod.cfg";
        public string DataDirectory { get; set; } = @"D:\SOMS\Data\MongoDB\data\";
        public string LogDirectory { get; set; } = @"D:\SOMS\Logs\";
        public string BackupDirectory { get; set; } = @"D:\SOMS\Backup\MongoDB\";

        // Enhanced monitoring and performance settings
        public int HealthCheckIntervalSeconds { get; set; } = 15; // More frequent health checks
        public int ProcessCheckIntervalSeconds { get; set; } = 2; // More frequent process checks
        public int MaxRestartAttempts { get; set; } = 5; // More restart attempts
        public int RestartDelaySeconds { get; set; } = 5; // Faster restart
        public int ConnectionTimeoutSeconds { get; set; } = 15; // Faster timeout detection
        public int MaxConnectionPoolSize { get; set; } = 200; // Larger pool for stability
        public int MinConnectionPoolSize { get; set; } = 10; // Higher minimum

        // Performance thresholds (optimized for stability)
        public long MaxMemoryUsageMB { get; set; } = 6144; // 6GB - higher threshold
        public int MaxActiveConnections { get; set; } = 150; // Higher connection limit
        public double MaxCpuUsagePercent { get; set; } = 90.0; // Higher CPU threshold

        // Stability-focused settings (disable data protection for performance)
        public bool EnableAutoBackup { get; set; } = false; // Disable backups for performance
        public int BackupRetentionDays { get; set; } = 3; // Shorter retention
        public bool EnablePerformanceOptimization { get; set; } = true;
        public int OptimizationIntervalHours { get; set; } = 12; // More frequent optimization
        public bool EnableDataIntegrityChecks { get; set; } = false; // Disable for performance
        public bool EnableJournaling { get; set; } = false; // Disable journaling for speed
        public bool EnableDataValidation { get; set; } = false; // Disable validation

        // Advanced monitoring settings
        public int SystemHangDetectionSeconds { get; set; } = 60; // System hang detection
        public int ProcessResponseTimeoutSeconds { get; set; } = 30; // Process response timeout
        public int MemoryOptimizationIntervalMinutes { get; set; } = 30; // Memory optimization frequency
        public bool EnableAdvancedProcessMonitoring { get; set; } = true; // Enhanced monitoring
        public bool EnableSystemRecovery { get; set; } = true; // System recovery features
        public int MaxSystemHangRecoveryAttempts { get; set; } = 3; // System hang recovery attempts
        public bool EnableEmergencyRestart { get; set; } = true; // Emergency restart capability

        // Memory optimization settings
        public long TargetMemoryUsageMB { get; set; } = 4096; // Target memory usage
        public bool EnableMemoryCompaction { get; set; } = true; // Memory compaction
        public bool EnableGarbageCollectionOptimization { get; set; } = true; // GC optimization
        public int MemoryPressureThresholdPercent { get; set; } = 80; // Memory pressure threshold

        // Alert settings
        public bool EnableEmailAlerts { get; set; } = false;
        public string AlertEmailRecipients { get; set; } = "";
        public string SmtpServer { get; set; } = "";
        public int SmtpPort { get; set; } = 587;
        public string SmtpUsername { get; set; } = "";
        public string SmtpPassword { get; set; } = "";
    }
}
