# Optimized MongoDB Process Monitor

## Overview

The Optimized MongoDB Process Monitor is an advanced console application specifically designed to monitor and protect MongoDB daemon (mongod) processes with a focus on **runtime stability over data integrity**. This enhanced version prioritizes MongoDB process uptime and performance optimization while removing data protection measures that may impact performance.

## ⚠️ IMPORTANT WARNINGS

**This application is optimized for STABILITY over DATA PROTECTION:**
- MongoDB journaling is **DISABLED** by default for performance
- Data integrity checks are **DISABLED** for speed
- Data validation is **DISABLED** for performance
- Automatic backups are **DISABLED** by default
- Various safety mechanisms are reduced or removed

**Use only in environments where:**
- Data loss risk is acceptable
- Performance and uptime are more critical than data safety
- You have alternative backup and recovery mechanisms
- Development, testing, or non-critical production environments

## Key Features

### 1. Enhanced Process Monitoring
- **Advanced process health detection** with deadlock identification
- **System responsiveness monitoring** to detect system hangs
- **Process resource usage tracking** (CPU, memory, threads, handles)
- **Hung thread detection** and analysis
- **I/O operation monitoring** for performance bottlenecks
- **Real-time process metrics** with historical tracking

### 2. Advanced Crash Recovery System
- **Multiple crash detection methods** for comprehensive coverage
- **Intelligent restart strategies** with escalating recovery procedures
- **System hang detection and recovery** for unresponsive systems
- **Emergency recovery procedures** when standard methods fail
- **Automatic process resurrection** with minimal downtime
- **Resource cleanup and recovery** after crashes

### 3. Memory Optimization Engine
- **MongoDB memory tuning** with cache optimization
- **Garbage collection optimization** for .NET runtime
- **Memory leak detection and prevention**
- **Dynamic memory allocation** based on system pressure
- **Process working set optimization**
- **Memory compaction** to reduce fragmentation

### 4. Stability-Focused Configuration
- **Disabled data integrity validation** for performance
- **Disabled journaling** for faster writes (configurable)
- **Optimized for runtime stability** over data safety
- **Reduced safety checks** for maximum performance
- **Performance-first MongoDB configuration** generation
- **Automatic configuration optimization**

### 5. System Recovery Management
- **System hang detection** with configurable thresholds
- **Process resurrection capabilities** for failed processes
- **Emergency restart procedures** with minimal safety checks
- **Resource cleanup and recovery** after system issues
- **Multi-level recovery strategies** from graceful to emergency
- **Automatic recovery attempt management**

### 6. Advanced Monitoring Capabilities
- **Enhanced process monitoring** with detailed metrics
- **System responsiveness checks** every 60 seconds (configurable)
- **Memory pressure monitoring** with automatic optimization
- **Performance threshold monitoring** with immediate response
- **Continuous health assessment** with minimal overhead
- **Real-time issue detection** and automatic correction

### 7. Aggressive Startup Reliability (NEW)
- **Multi-level escalating recovery** strategies for MongoDB startup
- **Automatic lock file cleanup** (mongod.lock, PID files, socket files)
- **Diagnostic data cleanup** (removes diagnostic.data folder contents)
- **Journal file cleanup** (removes corrupted journal files - DATA LOSS RISK)
- **Emergency startup procedures** with minimal configuration
- **Nuclear option recovery** (clears all non-essential data - MAXIMUM DATA LOSS RISK)
- **Startup verification** ensures MongoDB is actually responding, not just running
- **Comprehensive logging** of all cleanup actions and recovery attempts

## Configuration

### Optimized MongoDB Settings
```json
{
  "MongoDBSetting": {
    "Host": "127.0.0.1",
    "Port": "37017",
    "DatabaseName": "isas_mongodb",
    "IsAuth": "false",
    "UserName": "isasAdmin",
    "PassWord": "123456",

    // Enhanced monitoring settings (optimized for stability)
    "HealthCheckIntervalSeconds": 15,
    "ProcessCheckIntervalSeconds": 2,
    "MaxRestartAttempts": 5,
    "RestartDelaySeconds": 5,
    "ConnectionTimeoutSeconds": 15,
    "MaxConnectionPoolSize": 200,
    "MinConnectionPoolSize": 10,

    // Stability-focused settings (disable data protection for performance)
    "EnableDataIntegrityChecks": false,
    "EnableJournaling": false,
    "EnableDataValidation": false,
    "EnableAutoBackup": false,

    // Advanced monitoring settings
    "SystemHangDetectionSeconds": 60,
    "ProcessResponseTimeoutSeconds": 30,
    "MemoryOptimizationIntervalMinutes": 30,
    "EnableAdvancedProcessMonitoring": true,
    "EnableSystemRecovery": true,
    "MaxSystemHangRecoveryAttempts": 3,
    "EnableEmergencyRestart": true,

    // Memory optimization settings
    "TargetMemoryUsageMB": 4096,
    "EnableMemoryCompaction": true,
    "EnableGarbageCollectionOptimization": true,
    "MemoryPressureThresholdPercent": 80
  }
}
```

### Configuration Categories

#### File Paths
- `MongoDBExecutablePath`: Path to mongod.exe
- `MongoDBConfigPath`: Path to MongoDB configuration file (auto-generated if missing)
- `DataDirectory`: MongoDB data storage directory
- `LogDirectory`: Log file storage directory
- `BackupDirectory`: Backup storage directory

#### Enhanced Monitoring Settings (Optimized)
- `HealthCheckIntervalSeconds`: Health check frequency (default: 15s - more frequent)
- `ProcessCheckIntervalSeconds`: Process check frequency (default: 2s - more frequent)
- `MaxRestartAttempts`: Maximum restart attempts (default: 5 - more attempts)
- `RestartDelaySeconds`: Delay between restarts (default: 5s - faster restart)
- `SystemHangDetectionSeconds`: System hang detection threshold (default: 60s)
- `ProcessResponseTimeoutSeconds`: Process response timeout (default: 30s)

#### Performance Settings (Optimized)
- `ConnectionTimeoutSeconds`: Database connection timeout (default: 15s - faster detection)
- `MaxConnectionPoolSize`: Maximum connections in pool (default: 200 - larger pool)
- `MinConnectionPoolSize`: Minimum connections in pool (default: 10 - higher minimum)
- `MaxMemoryUsageMB`: Memory usage threshold (default: 6144MB - higher threshold)
- `MaxActiveConnections`: Active connection threshold (default: 150 - higher limit)
- `MaxCpuUsagePercent`: CPU usage threshold (default: 90% - higher threshold)

#### Stability-Focused Settings (Data Protection Disabled)
- `EnableDataIntegrityChecks`: Data integrity validation (default: false - DISABLED)
- `EnableJournaling`: MongoDB journaling (default: false - DISABLED for performance)
- `EnableDataValidation`: Document validation (default: false - DISABLED)
- `EnableAutoBackup`: Automatic backups (default: false - DISABLED for performance)
- `BackupRetentionDays`: Backup retention period (default: 3 days - shorter)

#### Memory Optimization Settings
- `TargetMemoryUsageMB`: Target memory usage (default: 4096MB)
- `MemoryOptimizationIntervalMinutes`: Memory optimization frequency (default: 30min)
- `EnableMemoryCompaction`: Memory compaction (default: true)
- `EnableGarbageCollectionOptimization`: GC optimization (default: true)
- `MemoryPressureThresholdPercent`: Memory pressure threshold (default: 80%)

#### System Recovery Settings
- `EnableAdvancedProcessMonitoring`: Enhanced monitoring (default: true)
- `EnableSystemRecovery`: System recovery features (default: true)
- `MaxSystemHangRecoveryAttempts`: System hang recovery attempts (default: 3)
- `EnableEmergencyRestart`: Emergency restart capability (default: true)

## Usage

### Running the Optimized MongoDB Monitor

1. **Standard Mode (Recommended)**:
   ```bash
   cd src\YunDa.Quick\RunMongoDB
   dotnet run
   ```

2. **Test Mode** (Validate configuration):
   ```bash
   dotnet run test
   ```

3. **Startup Reliability Test** (Test aggressive recovery):
   ```bash
   dotnet run startup-test
   ```

4. **Diagnostics Mode** (Check MongoDB setup):
   ```bash
   dotnet run diag
   ```

5. **Help** (Show available commands):
   ```bash
   dotnet run help
   ```

### Expected Console Output
```
[14:30:15 INF] === Optimized MongoDB Monitor Console Application Started ===
[14:30:15 INF] Application Version: 2.0.0 (Optimized for Stability)
[14:30:15 INF] Current Directory: D:\Project\SOMS\server\src\YunDa.Quick\RunMongoDB
[14:30:15 INF] Server GC Enabled: True
[14:30:15 INF] MongoDB Configuration (Optimized):
[14:30:15 INF]   Host: 127.0.0.1:37017
[14:30:15 INF]   Database: isas_mongodb
[14:30:15 INF]   Process Check Interval: 2s
[14:30:15 INF]   System Hang Detection: 60s
[14:30:15 INF]   Memory Optimization Interval: 30min
[14:30:15 WRN] === OPTIMIZATION WARNINGS ===
[14:30:15 WRN] This application is optimized for STABILITY over DATA PROTECTION
[14:30:15 WRN] WARNING: MongoDB journaling is DISABLED - reduced crash recovery
[14:30:15 WRN] WARNING: Data integrity checks are DISABLED - reduced data safety
[14:30:15 WRN] Starting optimized MongoDB monitoring and recovery service...
[14:30:15 INF] Press Ctrl+C to stop the application
```

### MongoDB Configuration File

Copy the provided `sample-mongod.cfg` to your MongoDB installation directory and modify the paths as needed:

```yaml
# Storage settings
storage:
  dbPath: "D:\\SOMS\\Data\\MongoDB\\data"
  journal:
    enabled: true

# Network settings
net:
  port: 37017
  bindIp: 127.0.0.1
  maxIncomingConnections: 200

# Logging
systemLog:
  destination: file
  path: "D:\\SOMS\\Logs\\mongod.log"
  logAppend: true
```

## Architecture

### Optimized Service Components

1. **OptimizedMongoDBWorker**: Main orchestrator with enhanced monitoring and recovery
2. **MongoDBProcessManager**: Enhanced process lifecycle management with emergency procedures
3. **SystemRecoveryManager**: Advanced system hang detection and recovery
4. **AdvancedMemoryOptimizer**: Comprehensive memory optimization and pressure monitoring
5. **EnhancedProcessMonitor**: Detailed process monitoring with deadlock detection
6. **MongoDBHealthMonitor**: Lightweight health checks focused on critical metrics
7. **MongoDBConfigurationService**: Automatic configuration optimization for stability

### Optimized Monitoring Flow

```
Start → Initial Setup → Optimized Configuration → Enhanced Monitoring Loop
                                                            ↓
Enhanced Process Monitor ← Memory Optimizer ← System Recovery Manager
         ↓                        ↓                    ↓
   Deadlock Detection    Memory Pressure Check    System Hang Detection
         ↓                        ↓                    ↓
   Issue Detection → Immediate Recovery Actions → Emergency Procedures
         ↓
   Escalating Recovery: Graceful → Aggressive → Emergency
```

### Recovery Strategy Levels

1. **Level 1 - Graceful Recovery**:
   - Standard process restart
   - Memory optimization
   - Configuration adjustment

2. **Level 2 - Aggressive Recovery**:
   - Force process termination
   - System cache clearing
   - Emergency memory cleanup

3. **Level 3 - Emergency Recovery**:
   - System hang recovery
   - Emergency process resurrection
   - Minimal safety checks

### Aggressive Startup Recovery Levels

The system implements 4 escalating levels of MongoDB startup recovery:

#### **Level 1: Standard Startup with Basic Cleanup**
- Removes basic lock files (mongod.lock, *.pid)
- Standard MongoDB startup attempt
- Minimal intervention approach

#### **Level 2: Aggressive File Cleanup**
- Removes ALL lock files, PID files, socket files
- Cleans diagnostic.data directory completely
- Removes journal files (⚠️ **DATA LOSS RISK**)
- Cleans temporary files
- Attempts startup after comprehensive cleanup

#### **Level 3: Emergency Startup**
- Performs Level 2 cleanup plus log file cleanup
- Generates emergency MongoDB configuration with minimal safety
- Disables journaling, validation, and safety checks
- Uses emergency startup flags (--nojournal, --smallfiles, --noprealloc)
- Bypasses most MongoDB safety mechanisms

#### **Level 4: Nuclear Option** (⚠️ **MAXIMUM DATA LOSS RISK**)
- Removes ALL non-essential files from data directory
- Preserves only core database files (*.bson, *.wt, WiredTiger*)
- Initializes fresh data directory structure
- Generates minimal configuration with absolute minimum settings
- Last resort when all other methods fail

Each level includes **startup verification** to ensure MongoDB is actually responding to commands, not just that the process started.

## Logging

The service provides structured logging with multiple levels:

- **Information**: Normal operations and status updates
- **Warning**: Non-critical issues that may need attention
- **Error**: Errors that don't stop the service but need investigation
- **Fatal**: Critical errors that stop the service

Logs are written to both console and file with rotation.

## Troubleshooting

### Common Issues

1. **MongoDB won't start**:
   - Check if data directory exists and has proper permissions
   - Verify MongoDB executable path is correct
   - Check for port conflicts

2. **High memory usage alerts**:
   - Review MongoDB configuration for cache settings
   - Consider increasing memory limits or optimizing queries

3. **Connection pool exhaustion**:
   - Increase MaxConnectionPoolSize
   - Review application connection usage patterns

4. **Frequent restarts**:
   - Check MongoDB logs for underlying issues
   - Verify system resources (disk space, memory)
   - Review performance thresholds

### Manual Intervention

If the service reaches maximum restart attempts:

1. Check MongoDB logs for error details
2. Verify system resources and disk space
3. Check data directory for corruption
4. Consider running MongoDB repair manually
5. Restart the management service after resolving issues

## Performance Recommendations

1. **Memory**: Allocate at least 4GB RAM for MongoDB
2. **Storage**: Use SSD storage for better I/O performance
3. **Network**: Ensure low latency between application and MongoDB
4. **Indexes**: Regularly review and optimize database indexes
5. **Monitoring**: Keep health check intervals reasonable (30-60 seconds)

## Security Considerations

1. **Authentication**: Enable MongoDB authentication in production
2. **Network**: Bind MongoDB to specific interfaces, not 0.0.0.0
3. **Encryption**: Consider enabling encryption at rest and in transit
4. **Firewall**: Restrict MongoDB port access to authorized systems
5. **Logs**: Secure log files and rotate them regularly

## Recent Optimizations (Console Application Focus)

### Program.cs Improvements
- **UTF-8 Encoding Support**: Added proper console encoding for Chinese character support
- **Enhanced Error Handling**: Comprehensive exception handling with graceful shutdown
- **Structured Logging**: Improved Serilog configuration with better formatting
- **Configuration Validation**: Added configuration file existence checks
- **Graceful Shutdown**: Proper Ctrl+C handling for clean application termination

### Worker.cs Improvements
- **Configuration-Driven Paths**: Removed hard-coded paths, now uses appsettings.json settings
- **Enhanced Process Management**: Better MongoDB process lifecycle management
- **Improved Health Checks**: More robust health checking with detailed logging
- **Restart Logic**: Intelligent restart attempts with configurable limits and delays
- **File Cleanup**: Comprehensive cleanup of lock files and diagnostic data
- **Structured Logging**: Replaced Chinese comments and logs with clear English messages

### Logging Enhancements
- **Console Output**: Clean, timestamped console output with log levels
- **File Logging**: Daily rotating log files with UTF-8 encoding
- **Structured Messages**: Parameterized logging for better log analysis
- **Debug Information**: Detailed debug logs for troubleshooting

### Console Application Features
- **Real-time Monitoring**: Live console output showing monitoring status
- **Configuration Display**: Startup logging of all configuration settings
- **Process Information**: Detailed process and health check information
- **Error Reporting**: Clear error messages with actionable information

## Usage (Optimized Console Version)

### Running the Application
```bash
cd src\YunDa.Quick\RunMongoDB
dotnet run
```

### Expected Console Output
```
[14:30:15 INF] === MongoDB Monitor Console Application Started ===
[14:30:15 INF] Application Version: 1.0.0
[14:30:15 INF] Current Directory: D:\Project\SOMS\server\src\YunDa.Quick\RunMongoDB
[14:30:15 INF] Server GC Enabled: True
[14:30:15 INF] MongoDB Configuration:
[14:30:15 INF]   Host: 127.0.0.1:37017
[14:30:15 INF]   Database: isas_mongodb
[14:30:15 INF]   Data Directory: D:\SOMS\Data\MongoDB\data\
[14:30:15 INF] Starting MongoDB monitoring loop...
[14:30:15 INF] Press Ctrl+C to stop the application
```

## Optimization Summary

### What Was Optimized

#### 1. Process Monitoring Enhancements
- **Frequency**: Process checks every 2 seconds (was 3 seconds)
- **Detection**: Added deadlock detection, hung thread monitoring
- **Metrics**: Enhanced with CPU, memory, I/O, and handle tracking
- **History**: Process metrics history for trend analysis

#### 2. Crash Recovery Improvements
- **Speed**: Restart delay reduced to 5 seconds (was 10 seconds)
- **Attempts**: Increased max restart attempts to 5 (was 3)
- **Methods**: Added emergency restart procedures
- **Recovery**: Multi-level recovery strategies (graceful → aggressive → emergency)

#### 3. System Recovery Features
- **Hang Detection**: System responsiveness monitoring every 60 seconds
- **Recovery**: Automatic system hang recovery procedures
- **Memory**: Force garbage collection and memory cleanup
- **Processes**: Kill unresponsive processes automatically

#### 4. Memory Optimization Engine
- **Frequency**: Memory optimization every 30 minutes
- **Pressure**: Automatic optimization when memory pressure > 80%
- **Techniques**: GC optimization, working set trimming, cache management
- **MongoDB**: MongoDB-specific memory optimization commands

#### 5. Data Protection Removal (For Performance)
- **Journaling**: Disabled by default for faster writes
- **Validation**: Disabled data integrity checks
- **Compression**: Disabled compression for speed
- **Logging**: Minimal logging for performance
- **Backups**: Disabled automatic backups

#### 6. Configuration Optimizations
- **Auto-Generation**: Automatic optimized configuration file creation
- **Cache Size**: Optimized WiredTiger cache settings
- **Connections**: Higher connection limits (200 vs 100)
- **Timeouts**: Faster timeout detection (15s vs 30s)

### Performance vs Safety Trade-offs

| Feature | Standard | Optimized | Impact |
|---------|----------|-----------|---------|
| Journaling | Enabled | **Disabled** | 🔴 Reduced crash recovery |
| Data Validation | Enabled | **Disabled** | 🔴 Allows invalid data |
| Compression | Enabled | **Disabled** | 🔴 Higher disk usage |
| Backup | Enabled | **Disabled** | 🔴 No automatic backups |
| Process Checks | 3s | **2s** | 🟢 Faster detection |
| Restart Delay | 10s | **5s** | 🟢 Faster recovery |
| Memory Optimization | 24h | **30min** | 🟢 Better performance |
| Connection Pool | 100 | **200** | 🟢 Higher throughput |

## Version History

- **v2.0.0**: Optimized MongoDB Process Monitor - Stability-focused with enhanced monitoring
  - Added SystemRecoveryManager for system hang detection
  - Added AdvancedMemoryOptimizer for comprehensive memory management
  - Added EnhancedProcessMonitor with deadlock detection
  - Added OptimizedMongoDBWorker with multi-level recovery
  - Disabled data protection features for maximum performance
  - Auto-generated optimized MongoDB configuration
  - Enhanced process monitoring with detailed metrics
- **v1.2**: Enhanced MongoDB Management Service with comprehensive monitoring
- **v1.1**: Console Application Optimization - Enhanced logging, configuration-driven paths
- **v1.0**: Basic MongoDB process monitoring and restart capabilities
