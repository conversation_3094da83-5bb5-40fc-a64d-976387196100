using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using Serilog;

namespace RunMongoDB.Services
{
    public class MongoDBConfigurationService
    {
        private readonly MongoDBSettings _settings;

        public MongoDBConfigurationService(MongoDBSettings settings)
        {
            _settings = settings;
        }

        /// <summary>
        /// Resolves and validates MongoDB configuration paths
        /// </summary>
        public MongoDBPathConfiguration ResolveConfigurationPaths()
        {
            var config = new MongoDBPathConfiguration();

            try
            {
                // Get the application base directory
                var baseDirectory = GetApplicationBaseDirectory();
                Log.Information("Application base directory: {BaseDirectory}", baseDirectory);

                // Resolve MongoDB executable path
                config.MongoDBExecutablePath = ResolveMongoDBExecutablePath(baseDirectory);
                
                // Resolve MongoDB configuration file path
                config.MongoDBConfigPath = ResolveMongoDBConfigPath(baseDirectory);

                // Resolve data directory
                config.DataDirectory = ResolveDataDirectory();

                // Resolve log directory
                config.LogDirectory = ResolveLogDirectory();

                // Resolve backup directory
                config.BackupDirectory = ResolveBackupDirectory();

                // Generate or optimize configuration for stability
                config.MongoDBConfigPath = EnsureOptimizedConfiguration(config);

                // Validate all paths
                ValidateConfiguration(config);

                Log.Information("MongoDB configuration paths resolved successfully");
                return config;
            }
            catch (Exception ex)
            {
                Log.Error("Error resolving MongoDB configuration paths: {Error}", ex.Message);
                throw;
            }
        }

        private string GetApplicationBaseDirectory()
        {
            // Try multiple methods to get the base directory
            var candidates = new[]
            {
                AppDomain.CurrentDomain.BaseDirectory,
                Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location),
                Directory.GetCurrentDirectory(),
                Environment.CurrentDirectory
            };

            foreach (var candidate in candidates.Where(c => !string.IsNullOrEmpty(c)))
            {
                if (Directory.Exists(candidate))
                {
                    return candidate;
                }
            }

            throw new DirectoryNotFoundException("Could not determine application base directory");
        }

        private string ResolveMongoDBExecutablePath(string baseDirectory)
        {
            // Priority order for finding MongoDB executable
            var searchPaths = new[]
            {
                _settings.MongoDBExecutablePath, // From configuration
                Path.Combine(baseDirectory, "MongoDB", "bin", "mongod.exe"),
                Path.Combine(baseDirectory, "大数据库", "mongod.exe"),
                @"D:\SOMS\大数据库\mongod.exe",
                @"C:\Program Files\MongoDB\Server\4.4\bin\mongod.exe",
                @"C:\Program Files\MongoDB\Server\5.0\bin\mongod.exe",
                @"C:\Program Files\MongoDB\Server\6.0\bin\mongod.exe",
                @"C:\MongoDB\bin\mongod.exe"
            };

            foreach (var path in searchPaths.Where(p => !string.IsNullOrEmpty(p)))
            {
                if (File.Exists(path))
                {
                    Log.Information("Found MongoDB executable at: {Path}", path);
                    return path;
                }
            }

            // If not found, use the configured path anyway (might be installed later)
            Log.Warning("MongoDB executable not found. Using configured path: {Path}", _settings.MongoDBExecutablePath);
            return _settings.MongoDBExecutablePath;
        }

        private string ResolveMongoDBConfigPath(string baseDirectory)
        {
            // Priority order for finding MongoDB config file
            var searchPaths = new[]
            {
                _settings.MongoDBConfigPath, // From configuration
                Path.Combine(baseDirectory, "mongod.cfg"),
                Path.Combine(baseDirectory, "sample-mongod.cfg"),
                Path.Combine(baseDirectory, "MongoDB", "mongod.cfg"),
                Path.Combine(baseDirectory, "大数据库", "mongod.cfg"),
                @"D:\SOMS\大数据库\mongod.cfg"
            };

            foreach (var path in searchPaths.Where(p => !string.IsNullOrEmpty(p)))
            {
                if (File.Exists(path))
                {
                    Log.Information("Found MongoDB config file at: {Path}", path);
                    return path;
                }
            }

            // If no config file found, return null to use command line arguments
            Log.Information("No MongoDB config file found. Will use command line arguments.");
            return null;
        }

        private string ResolveDataDirectory()
        {
            var candidates = new[]
            {
                _settings.DataDirectory,
                @"D:\SOMS\Data\MongoDB\data\",
                Path.Combine(GetApplicationBaseDirectory(), "Data", "MongoDB", "data"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "MongoDB", "data")
            };

            foreach (var candidate in candidates.Where(c => !string.IsNullOrEmpty(c)))
            {
                try
                {
                    if (!Directory.Exists(candidate))
                    {
                        Directory.CreateDirectory(candidate);
                    }
                    Log.Information("Using data directory: {Directory}", candidate);
                    return candidate;
                }
                catch (Exception ex)
                {
                    Log.Warning("Could not create data directory {Directory}: {Error}", candidate, ex.Message);
                }
            }

            throw new DirectoryNotFoundException("Could not resolve or create MongoDB data directory");
        }

        private string ResolveLogDirectory()
        {
            var candidates = new[]
            {
                _settings.LogDirectory,
                @"D:\SOMS\Logs\",
                Path.Combine(GetApplicationBaseDirectory(), "Logs"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "MongoDB", "logs")
            };

            foreach (var candidate in candidates.Where(c => !string.IsNullOrEmpty(c)))
            {
                try
                {
                    if (!Directory.Exists(candidate))
                    {
                        Directory.CreateDirectory(candidate);
                    }
                    Log.Information("Using log directory: {Directory}", candidate);
                    return candidate;
                }
                catch (Exception ex)
                {
                    Log.Warning("Could not create log directory {Directory}: {Error}", candidate, ex.Message);
                }
            }

            throw new DirectoryNotFoundException("Could not resolve or create MongoDB log directory");
        }

        private string ResolveBackupDirectory()
        {
            var candidates = new[]
            {
                _settings.BackupDirectory,
                @"D:\SOMS\Backup\MongoDB\",
                Path.Combine(GetApplicationBaseDirectory(), "Backup", "MongoDB"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "MongoDB", "backup")
            };

            foreach (var candidate in candidates.Where(c => !string.IsNullOrEmpty(c)))
            {
                try
                {
                    if (!Directory.Exists(candidate))
                    {
                        Directory.CreateDirectory(candidate);
                    }
                    Log.Information("Using backup directory: {Directory}", candidate);
                    return candidate;
                }
                catch (Exception ex)
                {
                    Log.Warning("Could not create backup directory {Directory}: {Error}", candidate, ex.Message);
                }
            }

            throw new DirectoryNotFoundException("Could not resolve or create MongoDB backup directory");
        }

        private void ValidateConfiguration(MongoDBPathConfiguration config)
        {
            var issues = new List<string>();

            // Check MongoDB executable
            if (string.IsNullOrEmpty(config.MongoDBExecutablePath) || !File.Exists(config.MongoDBExecutablePath))
            {
                issues.Add($"MongoDB executable not found: {config.MongoDBExecutablePath}");
            }

            // Check directories
            if (string.IsNullOrEmpty(config.DataDirectory) || !Directory.Exists(config.DataDirectory))
            {
                issues.Add($"Data directory not accessible: {config.DataDirectory}");
            }

            if (string.IsNullOrEmpty(config.LogDirectory) || !Directory.Exists(config.LogDirectory))
            {
                issues.Add($"Log directory not accessible: {config.LogDirectory}");
            }

            if (string.IsNullOrEmpty(config.BackupDirectory) || !Directory.Exists(config.BackupDirectory))
            {
                issues.Add($"Backup directory not accessible: {config.BackupDirectory}");
            }

            if (issues.Count > 0)
            {
                Log.Warning("Configuration validation issues found: {Issues}", string.Join(", ", issues));
                // Don't throw exception, just log warnings - some issues might be resolved at runtime
            }
        }

        /// <summary>
        /// Parses MongoDB configuration file if available
        /// </summary>
        public MongoDBConfigFileSettings ParseConfigFile(string configPath)
        {
            var settings = new MongoDBConfigFileSettings();

            if (string.IsNullOrEmpty(configPath) || !File.Exists(configPath))
            {
                Log.Information("No MongoDB config file to parse");
                return settings;
            }

            try
            {
                Log.Information("Parsing MongoDB config file: {ConfigPath}", configPath);
                var lines = File.ReadAllLines(configPath);
                
                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith("#"))
                        continue;

                    // Simple YAML-like parsing for basic settings
                    if (trimmedLine.Contains("port:"))
                    {
                        var portValue = ExtractValue(trimmedLine);
                        if (int.TryParse(portValue, out int port))
                        {
                            settings.Port = port;
                        }
                    }
                    else if (trimmedLine.Contains("bindIp:"))
                    {
                        settings.BindIp = ExtractValue(trimmedLine).Trim('"');
                    }
                    else if (trimmedLine.Contains("dbPath:"))
                    {
                        settings.DbPath = ExtractValue(trimmedLine).Trim('"');
                    }
                    else if (trimmedLine.Contains("path:") && trimmedLine.Contains("systemLog"))
                    {
                        settings.LogPath = ExtractValue(trimmedLine).Trim('"');
                    }
                }

                Log.Information("MongoDB config file parsed successfully");
                return settings;
            }
            catch (Exception ex)
            {
                Log.Error("Error parsing MongoDB config file: {Error}", ex.Message);
                return settings;
            }
        }

        private string ExtractValue(string line)
        {
            var colonIndex = line.IndexOf(':');
            if (colonIndex >= 0 && colonIndex < line.Length - 1)
            {
                return line.Substring(colonIndex + 1).Trim();
            }
            return string.Empty;
        }

        /// <summary>
        /// Ensures MongoDB configuration is optimized for stability and performance
        /// </summary>
        private string EnsureOptimizedConfiguration(MongoDBPathConfiguration config)
        {
            try
            {
                // If no config file exists, generate an optimized one
                if (string.IsNullOrEmpty(config.MongoDBConfigPath) || !File.Exists(config.MongoDBConfigPath))
                {
                    Log.Information("Generating optimized MongoDB configuration for stability");
                    return GenerateOptimizedConfigFile(config);
                }

                // If config file exists, optimize it
                Log.Information("Optimizing existing MongoDB configuration for stability");
                return OptimizeExistingConfigFile(config.MongoDBConfigPath, config);
            }
            catch (Exception ex)
            {
                Log.Error("Error optimizing MongoDB configuration: {Error}", ex.Message);
                return config.MongoDBConfigPath;
            }
        }

        /// <summary>
        /// Generates an optimized MongoDB configuration file focused on stability over data protection
        /// </summary>
        private string GenerateOptimizedConfigFile(MongoDBPathConfiguration config)
        {
            try
            {
                var configPath = Path.Combine(Path.GetDirectoryName(config.MongoDBExecutablePath), "mongod_optimized.cfg");
                var configContent = GenerateOptimizedConfigContent(config);

                File.WriteAllText(configPath, configContent, Encoding.UTF8);
                Log.Information("Generated optimized MongoDB configuration: {ConfigPath}", configPath);

                return configPath;
            }
            catch (Exception ex)
            {
                Log.Error("Error generating optimized config file: {Error}", ex.Message);
                return config.MongoDBConfigPath;
            }
        }

        /// <summary>
        /// Generates optimized configuration content prioritizing stability and performance
        /// </summary>
        private string GenerateOptimizedConfigContent(MongoDBPathConfiguration config)
        {
            var sb = new StringBuilder();

            // Header with warnings
            sb.AppendLine("# MongoDB Configuration - Optimized for Stability and Performance");
            sb.AppendLine("# WARNING: This configuration prioritizes performance over data safety");
            sb.AppendLine("# Generated by RunMongoDB Enhanced Process Monitor");
            sb.AppendLine();

            // Storage settings - optimized for performance over safety
            sb.AppendLine("storage:");
            sb.AppendLine($"  dbPath: \"{config.DataDirectory.Replace("\\", "/")}\"");

            // Disable journaling for better performance (less data protection)
            if (!_settings.EnableJournaling)
            {
                sb.AppendLine("  journal:");
                sb.AppendLine("    enabled: false  # Disabled for performance - reduces data safety");
            }

            sb.AppendLine("  engine: wiredTiger");
            sb.AppendLine("  wiredTiger:");
            sb.AppendLine("    engineConfig:");
            sb.AppendLine($"      cacheSizeGB: {_settings.TargetMemoryUsageMB / 1024.0:F1}");
            sb.AppendLine("      statisticsLogDelaySecs: 0  # Disable statistics logging");
            sb.AppendLine("      directoryForIndexes: false");
            sb.AppendLine("    collectionConfig:");
            sb.AppendLine("      blockCompressor: none  # Disable compression for speed");
            sb.AppendLine("    indexConfig:");
            sb.AppendLine("      prefixCompression: false  # Disable compression for speed");
            sb.AppendLine();

            // Network settings - optimized for high throughput
            sb.AppendLine("net:");
            sb.AppendLine($"  port: {_settings.Port}");
            sb.AppendLine($"  bindIp: {_settings.Host}");
            sb.AppendLine($"  maxIncomingConnections: {_settings.MaxConnectionPoolSize * 2}");
            sb.AppendLine("  wireObjectCheck: false  # Disable for performance");
            sb.AppendLine();

            // System log settings - minimal logging for performance
            sb.AppendLine("systemLog:");
            sb.AppendLine("  destination: file");
            sb.AppendLine($"  path: \"{Path.Combine(config.LogDirectory, "mongod.log").Replace("\\", "/")}\"");
            sb.AppendLine("  logAppend: true");
            sb.AppendLine("  logRotate: reopen");
            sb.AppendLine("  verbosity: 0  # Minimal logging for performance");
            sb.AppendLine("  quiet: true  # Reduce log output");
            sb.AppendLine();

            // Process management
            sb.AppendLine("processManagement:");
            sb.AppendLine("  fork: false");
            sb.AppendLine();

            // Operation profiling - disabled for performance
            sb.AppendLine("operationProfiling:");
            sb.AppendLine("  mode: off  # Disabled for performance");
            sb.AppendLine();

            // Disable security features for maximum performance (if auth is disabled)
            if (_settings.IsAuth != "true")
            {
                sb.AppendLine("# Security disabled for maximum performance");
                sb.AppendLine("# WARNING: This is not suitable for production environments");
                sb.AppendLine("security:");
                sb.AppendLine("  authorization: disabled");
            }

            return sb.ToString();
        }

        /// <summary>
        /// Optimizes existing configuration file for stability
        /// </summary>
        private string OptimizeExistingConfigFile(string configPath, MongoDBPathConfiguration config)
        {
            try
            {
                if (!File.Exists(configPath))
                    return configPath;

                Log.Information("Optimizing existing MongoDB configuration for stability");

                var content = File.ReadAllText(configPath);
                var lines = content.Split('\n').ToList();

                // Create backup
                var backupPath = configPath + ".backup." + DateTime.Now.ToString("yyyyMMdd_HHmmss");
                File.Copy(configPath, backupPath);
                Log.Information("Created configuration backup: {BackupPath}", backupPath);

                // Apply optimizations
                var optimizedLines = ApplyConfigurationOptimizations(lines);

                // Write optimized configuration
                File.WriteAllLines(configPath, optimizedLines, Encoding.UTF8);
                Log.Information("Optimized existing configuration file");

                return configPath;
            }
            catch (Exception ex)
            {
                Log.Warning("Could not optimize existing config file: {Error}", ex.Message);
                return configPath;
            }
        }

        /// <summary>
        /// Applies optimization rules to configuration lines
        /// </summary>
        private List<string> ApplyConfigurationOptimizations(List<string> lines)
        {
            var optimizedLines = new List<string>();
            bool inJournalSection = false;
            bool journalDisabled = false;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();

                // Skip or modify lines based on optimization settings
                if (trimmedLine.Contains("journal:"))
                {
                    inJournalSection = true;
                    if (!_settings.EnableJournaling)
                    {
                        optimizedLines.Add("  journal:");
                        optimizedLines.Add("    enabled: false  # Disabled for performance");
                        journalDisabled = true;
                        continue;
                    }
                }
                else if (inJournalSection && trimmedLine.Contains("enabled:") && !_settings.EnableJournaling)
                {
                    if (!journalDisabled)
                    {
                        optimizedLines.Add("    enabled: false  # Disabled for performance");
                    }
                    continue;
                }
                else if (trimmedLine.Contains("wireObjectCheck:"))
                {
                    optimizedLines.Add("  wireObjectCheck: false  # Disabled for performance");
                    continue;
                }
                else if (trimmedLine.Contains("blockCompressor:"))
                {
                    optimizedLines.Add("      blockCompressor: none  # Disabled for performance");
                    continue;
                }
                else if (trimmedLine.Contains("prefixCompression:"))
                {
                    optimizedLines.Add("      prefixCompression: false  # Disabled for performance");
                    continue;
                }
                else if (trimmedLine.Contains("verbosity:"))
                {
                    optimizedLines.Add("  verbosity: 0  # Minimal logging for performance");
                    continue;
                }

                optimizedLines.Add(line);
            }

            return optimizedLines;
        }
    }

    public class MongoDBPathConfiguration
    {
        public string MongoDBExecutablePath { get; set; }
        public string MongoDBConfigPath { get; set; }
        public string DataDirectory { get; set; }
        public string LogDirectory { get; set; }
        public string BackupDirectory { get; set; }
    }

    public class MongoDBConfigFileSettings
    {
        public int? Port { get; set; }
        public string BindIp { get; set; }
        public string DbPath { get; set; }
        public string LogPath { get; set; }
    }
}
