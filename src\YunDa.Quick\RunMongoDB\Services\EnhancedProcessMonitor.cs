using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Management;
using Serilog;

namespace RunMongoDB.Services
{
    /// <summary>
    /// Enhanced process monitoring service with advanced detection capabilities
    /// for MongoDB process health, deadlocks, and performance issues
    /// </summary>
    public class EnhancedProcessMonitor
    {
        private readonly MongoDBSettings _settings;
        private readonly Dictionary<int, ProcessMetrics> _processHistory;
        private DateTime _lastMonitoringTime = DateTime.MinValue;
        private bool _isMonitoringActive = false;

        public EnhancedProcessMonitor(MongoDBSettings settings)
        {
            _settings = settings;
            _processHistory = new Dictionary<int, ProcessMetrics>();
            Log.Information("Enhanced Process Monitor initialized with advanced detection capabilities");
        }

        /// <summary>
        /// Performs comprehensive MongoDB process monitoring
        /// </summary>
        public async Task<ProcessMonitoringResult> MonitorMongoDBProcessesAsync()
        {
            var result = new ProcessMonitoringResult
            {
                MonitoringTime = DateTime.Now,
                IsHealthy = false,
                Issues = new List<string>()
            };

            try
            {
                _isMonitoringActive = true;
                Log.Debug("Starting enhanced MongoDB process monitoring");

                // Get all MongoDB processes
                var mongoProcesses = Process.GetProcessesByName("mongod");
                result.ProcessCount = mongoProcesses.Length;

                if (mongoProcesses.Length == 0)
                {
                    result.Issues.Add("No MongoDB processes found");
                    return result;
                }

                // Monitor each process
                var processResults = new List<ProcessMetrics>();
                foreach (var process in mongoProcesses)
                {
                    try
                    {
                        var metrics = await AnalyzeProcessAsync(process);
                        processResults.Add(metrics);
                        
                        // Update process history
                        _processHistory[process.Id] = metrics;
                    }
                    catch (Exception ex)
                    {
                        Log.Warning("Error analyzing process {ProcessId}: {Error}", process.Id, ex.Message);
                        result.Issues.Add($"Error analyzing process {process.Id}: {ex.Message}");
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                // Analyze overall health
                result.ProcessMetrics = processResults;
                result.IsHealthy = AnalyzeOverallHealth(processResults, result.Issues);
                
                // Detect deadlocks and hangs
                await DetectDeadlocksAndHangsAsync(processResults, result.Issues);

                // Check for resource exhaustion
                CheckResourceExhaustion(processResults, result.Issues);

                _lastMonitoringTime = DateTime.Now;
                
                if (result.IsHealthy)
                {
                    Log.Debug("MongoDB process monitoring completed - all processes healthy");
                }
                else
                {
                    Log.Warning("MongoDB process monitoring detected issues: {Issues}", 
                        string.Join(", ", result.Issues));
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.Error("Error during MongoDB process monitoring: {Error}", ex.Message);
                result.Issues.Add($"Monitoring error: {ex.Message}");
                return result;
            }
            finally
            {
                _isMonitoringActive = false;
            }
        }

        /// <summary>
        /// Analyzes individual MongoDB process metrics
        /// </summary>
        private async Task<ProcessMetrics> AnalyzeProcessAsync(Process process)
        {
            var metrics = new ProcessMetrics
            {
                ProcessId = process.Id,
                ProcessName = process.ProcessName,
                StartTime = process.StartTime,
                IsResponding = process.Responding
            };

            try
            {
                // Basic metrics
                metrics.MemoryUsageMB = process.WorkingSet64 / 1024 / 1024;
                metrics.VirtualMemoryMB = process.VirtualMemorySize64 / 1024 / 1024;
                metrics.ThreadCount = process.Threads.Count;
                metrics.HandleCount = process.HandleCount;

                // CPU usage calculation
                metrics.CpuUsagePercent = await CalculateCpuUsageAsync(process);

                // Advanced metrics
                metrics.PagedMemoryMB = process.PagedMemorySize64 / 1024 / 1024;
                metrics.NonPagedMemoryMB = process.NonpagedSystemMemorySize64 / 1024 / 1024;

                // Check for hung threads
                metrics.HungThreadCount = await CountHungThreadsAsync(process);

                // Check I/O operations
                await AnalyzeIOMetricsAsync(process, metrics);

                // Performance counters
                await GetPerformanceCountersAsync(process, metrics);

                Log.Debug("Process {ProcessId} metrics: Memory={MemoryUsage}MB, CPU={CpuUsage}%, Threads={ThreadCount}", 
                    process.Id, metrics.MemoryUsageMB, metrics.CpuUsagePercent, metrics.ThreadCount);

            }
            catch (Exception ex)
            {
                Log.Warning("Error getting metrics for process {ProcessId}: {Error}", process.Id, ex.Message);
                metrics.HasErrors = true;
                metrics.ErrorMessage = ex.Message;
            }

            return metrics;
        }

        /// <summary>
        /// Calculates CPU usage for a process
        /// </summary>
        private async Task<double> CalculateCpuUsageAsync(Process process)
        {
            try
            {
                var startTime = DateTime.UtcNow;
                var startCpuUsage = process.TotalProcessorTime;

                await Task.Delay(500); // Wait 500ms

                var endTime = DateTime.UtcNow;
                var endCpuUsage = process.TotalProcessorTime;

                var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
                var totalMsPassed = (endTime - startTime).TotalMilliseconds;
                var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);

                return cpuUsageTotal * 100;
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Counts hung threads in a process
        /// </summary>
        private async Task<int> CountHungThreadsAsync(Process process)
        {
            try
            {
                return await Task.Run(() =>
                {
                    int hungThreads = 0;
                    foreach (ProcessThread thread in process.Threads)
                    {
                        try
                        {
                            if (thread.ThreadState == ThreadState.Wait && 
                                thread.WaitReason == ThreadWaitReason.Suspended)
                            {
                                hungThreads++;
                            }
                        }
                        catch
                        {
                            // Ignore errors accessing thread information
                        }
                    }
                    return hungThreads;
                });
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Analyzes I/O metrics for a process
        /// </summary>
        private async Task AnalyzeIOMetricsAsync(Process process, ProcessMetrics metrics)
        {
            try
            {
                await Task.Run(() =>
                {
                    try
                    {
                        // Get I/O counters using WMI
                        var query = $"SELECT * FROM Win32_Process WHERE ProcessId = {process.Id}";
                        using (var searcher = new ManagementObjectSearcher(query))
                        {
                            foreach (ManagementObject obj in searcher.Get())
                            {
                                try
                                {
                                    metrics.ReadOperationCount = Convert.ToInt64(obj["ReadOperationCount"] ?? 0);
                                    metrics.WriteOperationCount = Convert.ToInt64(obj["WriteOperationCount"] ?? 0);
                                    metrics.ReadTransferCount = Convert.ToInt64(obj["ReadTransferCount"] ?? 0);
                                    metrics.WriteTransferCount = Convert.ToInt64(obj["WriteTransferCount"] ?? 0);
                                }
                                catch
                                {
                                    // Ignore errors getting I/O metrics
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Debug("WMI not available for I/O metrics: {Error}", ex.Message);

                        // Set default values when WMI is not available
                        metrics.ReadOperationCount = 0;
                        metrics.WriteOperationCount = 0;
                        metrics.ReadTransferCount = 0;
                        metrics.WriteTransferCount = 0;
                    }
                });
            }
            catch (Exception ex)
            {
                Log.Debug("Could not get I/O metrics for process {ProcessId}: {Error}", process.Id, ex.Message);

                // Set default values
                metrics.ReadOperationCount = 0;
                metrics.WriteOperationCount = 0;
                metrics.ReadTransferCount = 0;
                metrics.WriteTransferCount = 0;
            }
        }

        /// <summary>
        /// Gets performance counters for a process
        /// </summary>
        private async Task GetPerformanceCountersAsync(Process process, ProcessMetrics metrics)
        {
            try
            {
                await Task.Run(() =>
                {
                    try
                    {
                        var processName = process.ProcessName;

                        // Try to get page faults per second using performance counter
                        using (var counter = new PerformanceCounter("Process", "Page Faults/sec", processName))
                        {
                            counter.NextValue(); // First call returns 0
                            Task.Delay(100).Wait();
                            metrics.PageFaultsPerSecond = counter.NextValue();
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Debug("Performance counter not available for process {ProcessName}: {Error}",
                            process.ProcessName, ex.Message);

                        // Set default value when performance counter is not available
                        metrics.PageFaultsPerSecond = 0;
                    }
                });
            }
            catch (Exception ex)
            {
                Log.Debug("Could not get performance counters for process {ProcessId}: {Error}", process.Id, ex.Message);
                metrics.PageFaultsPerSecond = 0;
            }
        }

        /// <summary>
        /// Analyzes overall health of all MongoDB processes
        /// </summary>
        private bool AnalyzeOverallHealth(List<ProcessMetrics> processMetrics, List<string> issues)
        {
            bool isHealthy = true;

            foreach (var metrics in processMetrics)
            {
                // Check if process is responding
                if (!metrics.IsResponding)
                {
                    issues.Add($"Process {metrics.ProcessId} is not responding");
                    isHealthy = false;
                }

                // Check memory usage
                if (metrics.MemoryUsageMB > _settings.MaxMemoryUsageMB)
                {
                    issues.Add($"Process {metrics.ProcessId} memory usage too high: {metrics.MemoryUsageMB}MB");
                    isHealthy = false;
                }

                // Check CPU usage
                if (metrics.CpuUsagePercent > _settings.MaxCpuUsagePercent)
                {
                    issues.Add($"Process {metrics.ProcessId} CPU usage too high: {metrics.CpuUsagePercent:F1}%");
                    isHealthy = false;
                }

                // Check for hung threads
                if (metrics.HungThreadCount > 5) // Threshold for hung threads
                {
                    issues.Add($"Process {metrics.ProcessId} has {metrics.HungThreadCount} hung threads");
                    isHealthy = false;
                }

                // Check for errors
                if (metrics.HasErrors)
                {
                    issues.Add($"Process {metrics.ProcessId} has errors: {metrics.ErrorMessage}");
                    isHealthy = false;
                }
            }

            return isHealthy;
        }

        /// <summary>
        /// Detects deadlocks and hangs in MongoDB processes
        /// </summary>
        private async Task DetectDeadlocksAndHangsAsync(List<ProcessMetrics> processMetrics, List<string> issues)
        {
            try
            {
                foreach (var metrics in processMetrics)
                {
                    // Check for potential deadlocks (high CPU with no responsiveness)
                    if (metrics.CpuUsagePercent > 80 && !metrics.IsResponding)
                    {
                        issues.Add($"Potential deadlock detected in process {metrics.ProcessId}");
                    }

                    // Check for memory leaks (continuously increasing memory)
                    if (_processHistory.ContainsKey(metrics.ProcessId))
                    {
                        var previousMetrics = _processHistory[metrics.ProcessId];
                        var memoryIncrease = metrics.MemoryUsageMB - previousMetrics.MemoryUsageMB;
                        
                        if (memoryIncrease > 100) // More than 100MB increase
                        {
                            issues.Add($"Potential memory leak in process {metrics.ProcessId}: +{memoryIncrease}MB");
                        }
                    }

                    // Check for excessive thread creation
                    if (metrics.ThreadCount > 200) // Threshold for thread count
                    {
                        issues.Add($"Excessive thread count in process {metrics.ProcessId}: {metrics.ThreadCount}");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Warning("Error detecting deadlocks and hangs: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Checks for resource exhaustion
        /// </summary>
        private void CheckResourceExhaustion(List<ProcessMetrics> processMetrics, List<string> issues)
        {
            try
            {
                var totalMemoryUsage = processMetrics.Sum(m => m.MemoryUsageMB);
                var totalThreads = processMetrics.Sum(m => m.ThreadCount);
                var totalHandles = processMetrics.Sum(m => m.HandleCount);

                if (totalMemoryUsage > _settings.MaxMemoryUsageMB * 1.5) // 150% of limit
                {
                    issues.Add($"Total memory usage exceeds safe limits: {totalMemoryUsage}MB");
                }

                if (totalThreads > 1000) // High thread count threshold
                {
                    issues.Add($"Total thread count is very high: {totalThreads}");
                }

                if (totalHandles > 10000) // High handle count threshold
                {
                    issues.Add($"Total handle count is very high: {totalHandles}");
                }
            }
            catch (Exception ex)
            {
                Log.Warning("Error checking resource exhaustion: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Gets the current monitoring status
        /// </summary>
        public MonitoringStatus GetMonitoringStatus()
        {
            return new MonitoringStatus
            {
                IsActive = _isMonitoringActive,
                LastMonitoringTime = _lastMonitoringTime,
                ProcessHistoryCount = _processHistory.Count,
                MonitoringIntervalSeconds = _settings.ProcessCheckIntervalSeconds
            };
        }

        public bool IsMonitoringActive => _isMonitoringActive;
        public DateTime LastMonitoringTime => _lastMonitoringTime;
        public int ProcessHistoryCount => _processHistory.Count;
    }

    /// <summary>
    /// Process monitoring result
    /// </summary>
    public class ProcessMonitoringResult
    {
        public DateTime MonitoringTime { get; set; }
        public bool IsHealthy { get; set; }
        public int ProcessCount { get; set; }
        public List<string> Issues { get; set; } = new List<string>();
        public List<ProcessMetrics> ProcessMetrics { get; set; } = new List<ProcessMetrics>();
    }

    /// <summary>
    /// Individual process metrics
    /// </summary>
    public class ProcessMetrics
    {
        public int ProcessId { get; set; }
        public string ProcessName { get; set; }
        public DateTime StartTime { get; set; }
        public bool IsResponding { get; set; }
        public long MemoryUsageMB { get; set; }
        public long VirtualMemoryMB { get; set; }
        public long PagedMemoryMB { get; set; }
        public long NonPagedMemoryMB { get; set; }
        public int ThreadCount { get; set; }
        public int HandleCount { get; set; }
        public double CpuUsagePercent { get; set; }
        public int HungThreadCount { get; set; }
        public long ReadOperationCount { get; set; }
        public long WriteOperationCount { get; set; }
        public long ReadTransferCount { get; set; }
        public long WriteTransferCount { get; set; }
        public float PageFaultsPerSecond { get; set; }
        public bool HasErrors { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Monitoring status information
    /// </summary>
    public class MonitoringStatus
    {
        public bool IsActive { get; set; }
        public DateTime LastMonitoringTime { get; set; }
        public int ProcessHistoryCount { get; set; }
        public int MonitoringIntervalSeconds { get; set; }
    }
}
