# FeederMonitoringAppService 遥测存储系统集成修改总结

## 修改概述

本次修改将 `FeederMonitoringAppService` 的数据源实现从硬编码 Redis 查询和自定义 MongoDB 集合查询，升级为使用完整的遥测存储系统架构，与 `TransformerMonitoringAppService` 保持一致的设计模式。

## 主要变更

### 1. 依赖注入更新

**新增依赖项：**
- `ITelemeteringBucketQueryService _bucketQueryService` - 遥测分桶查询服务
- `IMongoDbRepository<TelemeteringResult, Guid> _telemeteringResultRepository` - 遥测结果仓储
- `IMongoDbRepository<TelemeteringStatisticsResult, Guid> _statisticsRepository` - 遥测统计结果仓储

**新增命名空间：**
- `Yunda.ISAS.MongoDB.Entities.DataMonitoring`

### 2. 数据检索逻辑重构

#### 原有实现问题：
- **硬编码 Redis 键**：使用 `"telemeteringModelList_Zongzi"` 直接查询
- **自定义集合命名**：使用非标准的 MongoDB 集合命名模式
- **多种集合尝试**：在 `GetAllFeederCurrentStats()` 中尝试多种集合命名模式
- **无统一数据源策略**：不同方法使用不同的数据获取策略

#### 新实现特性：
- **统一数据源接口**：所有方法使用相同的遥测存储系统接口
- **智能数据源选择**：根据查询类型自动选择最适合的数据源
- **分层降级机制**：遥测存储系统 → 传统 MongoDB → 模拟数据
- **向后兼容性**：保留传统 MongoDB 查询作为降级方案

### 3. 方法级别修改详情

#### `GetFeederPowerComposition()` - 馈线功率组成
**原有实现：**
```csharp
var redisKey = "telemeteringModelList_Zongzi";
var telemeterings = await _telemeteringModelListRedis.HashSetGetAllAsync(redisKey);
var activeTelemetry = telemeterings.FirstOrDefault(t => t.Id == activeConfig.TelemeteringId);
```

**新实现：**
```csharp
DateTime currentTime = DateTime.Now;
DateTime startTime = currentTime.AddMinutes(-5);
DateTime endTime = currentTime;
var activePowerData = await GetLatestTelemetryValue(activeConfig.TelemeteringId, startTime, endTime);
```

#### `GetFeederPowerFactors()` - 馈线功率因数
**原有实现：**
```csharp
var redisKey = "telemeteringModelList_Zongzi";
var telemeterings = await _telemeteringModelListRedis.HashSetGetAllAsync(redisKey);
var powerFactorTelemetry = telemeterings.FirstOrDefault(t => t.Id == powerFactorConfig.TelemeteringId);
```

**新实现：**
```csharp
DateTime currentTime = DateTime.Now;
DateTime startTime = currentTime.AddMinutes(-5);
DateTime endTime = currentTime;
var powerFactorValue = await GetLatestTelemetryValue(powerFactorConfig.TelemeteringId, startTime, endTime);
```

#### `GetFeederEnergyConsumption()` - 馈线能耗数据
**原有实现：**
```csharp
_mongoRepository.CollectionName = $"TelemeteringModel_PowerConsumptionData_{intervalSuffix}_{dateSuffix}";
var powerConsumptionDocs = _mongoRepository.GetAllIncludeToFindFluent(filter).ToList();
```

**新实现：**
```csharp
var telemeteringConfigIds = feederConfigs.Where(c => c.TelemeteringConfiguration != null)
    .Select(c => c.TelemeteringId).ToList();
var telemetryData = await GetTelemetryDataByType(telemeteringConfigIds, startTime, endTime, interval);
```

#### `GetAllFeederCurrentStats()` - 馈线电流统计
**原有实现：**
```csharp
var collectionNames = new List<string>
{
    $"TelemeteringModel_Zongzi_{DateTime.Now:yyyyMMdd}",
    $"TelemeteringModel_Zongzi{DateTime.Now:yyyyMMdd}",
    // ... 多种命名模式尝试
};
```

**新实现：**
```csharp
var telemetryIds = telemeteringConfigIds.Keys.ToList();
var telemetryData = await GetTelemetryDataByType(telemetryIds, startTime, endTime, interval);
// 降级机制：await QueryLegacyCurrentData(...);
```

### 4. 新增核心方法

#### `GetLatestTelemetryValue()` - 获取最新遥测数值
专门用于获取单个遥测点的最新数值，支持：
- Redis 实时数据优先
- 分桶存储数据降级
- 异常处理和日志记录

#### `GetTelemetryDataByType()` - 智能数据源选择
根据查询类型自动选择数据源：
- **实时查询** → Redis 缓存
- **小时/日查询** → 分桶存储 (TelemeteringBucket)
- **周/月/年查询** → 统计数据 (TelemeteringStatisticsBucket)

#### `GetRealTimeDataFromRedis()` - Redis 实时数据获取
优化的 Redis 数据查询，支持单点遥测数据获取

#### `GetStatisticsData()` - 统计数据查询
利用遥测存储系统的统计功能，支持多种统计类型

#### `MapPowerTypeToInterval()` - 查询类型映射
将业务查询类型映射到存储系统的时间间隔

#### `QueryLegacyCurrentData()` - 传统数据查询（向后兼容）
保留原有的多集合命名模式查询，作为降级方案

### 5. 数据源架构对比

#### 原有架构：
```
方法1: Redis (实时) → 模拟数据
方法2: 自定义MongoDB集合 → 模拟数据
方法3: 多种MongoDB集合尝试 → 模拟数据
方法4: 复杂的集合命名模式 → 模拟数据
```

#### 新架构：
```
统一查询接口 → 
├── 实时查询 → Redis 缓存
├── 短期查询 → 分桶存储 (按月分表)
├── 长期查询 → 统计数据 (按年分表)
├── 降级处理 → 传统MongoDB集合
└── 最终降级 → 智能模拟数据
```

### 6. 存储策略集成

#### 分桶存储集成：
- **集合命名**：`TelemeteringBucket_{DataSourceCategory}_{yyyyMM}`
- **数据源支持**：Zongzi(综自)、Peidian(配电)、Fukong(辅控)、ZXJC(在线监测)、Robot(机器人)
- **时间分片**：按小时分桶，每桶最多3600个测量点

#### 统计数据集成：
- **集合命名**：`TelemeteringStatisticsBucket_{Interval}_{yyyy}`
- **统计类型**：实时值、最大值、最小值、平均值、差值、累计值、标准差、中位数
- **时间间隔**：1分钟到30天的多种间隔

#### 向后兼容性：
- **传统集合**：保留对原有集合命名模式的支持
- **降级查询**：`QueryLegacyCurrentData()` 方法处理传统数据源
- **多模式尝试**：支持多种集合命名模式的自动尝试

### 7. 性能优化

#### 查询优化：
- **单点查询优化**：`GetLatestTelemetryValue()` 专门优化单值查询
- **批量查询支持**：`GetTelemetryDataByType()` 支持多遥测点并行查询
- **时间范围优化**：精确计算查询时间窗口
- **缓存策略**：优先使用 Redis 实时数据

#### 错误处理：
- **分层降级**：遥测存储系统 → 传统MongoDB → 模拟数据
- **异常隔离**：单个遥测点错误不影响整体查询
- **详细日志**：每个数据源的查询结果都有详细日志

### 8. 兼容性保证

#### 接口兼容性：
- 所有公共方法签名保持不变
- 返回数据格式完全兼容
- 现有调用代码无需修改

#### 功能兼容性：
- 保留原有的模拟数据生成作为最终降级方案
- 保持原有的时间格式化和数据处理逻辑
- 维持原有的业务逻辑和计算规则
- 支持原有的集合命名模式（向后兼容）

### 9. 配置要求

#### 依赖注入配置：
需要在 DI 容器中注册新的服务：
```csharp
services.AddScoped<ITelemeteringBucketQueryService, TelemeteringBucketQueryService>();
services.AddScoped<IMongoDbRepository<TelemeteringResult, Guid>, MongoDbRepository<TelemeteringResult, Guid>>();
services.AddScoped<IMongoDbRepository<TelemeteringStatisticsResult, Guid>, MongoDbRepository<TelemeteringStatisticsResult, Guid>>();
```

#### 数据库配置：
确保 MongoDB 中存在相应的集合和索引：
- TelemeteringBucket 系列集合
- TelemeteringStatisticsBucket 系列集合
- 传统集合（向后兼容）
- 相应的复合索引

### 10. 测试建议

#### 单元测试：
- 测试各种查询类型的数据源选择逻辑
- 验证降级机制的正确性
- 测试传统集合查询的兼容性

#### 集成测试：
- 验证与遥测存储系统的集成
- 测试不同数据源的查询性能
- 验证数据一致性和准确性

#### 兼容性测试：
- 测试传统数据源的降级查询
- 验证多种集合命名模式的支持
- 测试异常情况下的降级机制

### 11. 监控指标

建议添加以下监控指标：
- 各数据源的查询次数和响应时间
- 降级查询的触发频率
- 传统集合查询的使用情况
- 模拟数据生成的频率（作为数据质量指标）
- 查询错误率和降级率

## 总结

本次修改成功将 FeederMonitoringAppService 从多种不一致的数据源实现统一为完整的遥测存储系统架构，实现了：

1. **架构统一化**：与 TransformerMonitoringAppService 保持一致的设计模式
2. **数据源智能化**：根据查询类型自动选择最优数据源
3. **降级机制完善**：多层降级确保服务可用性和向后兼容性
4. **性能优化**：利用分桶存储和统计预计算提升查询效率
5. **兼容性保证**：完全向后兼容，支持渐进式迁移

修改后的服务能够提供更准确、更高效、更可靠的馈线监控数据查询功能，同时保持与现有系统的完全兼容性。
