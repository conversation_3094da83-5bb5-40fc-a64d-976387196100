<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <UserSecretsId>dotnet-RunMongoDB-F084529C-6E8B-44A8-BE09-0A65470BDFBE</UserSecretsId>
    <ApplicationIcon />
    <OutputType>Exe</OutputType>
    <StartupObject>RunMongoDB.OptimizedProgram</StartupObject>
    <AssemblyName>OptimizedMongoDBMonitor</AssemblyName>
	  <SatelliteResourceLanguages>zh-Hans</SatelliteResourceLanguages>
	  <PlatformTarget>x64</PlatformTarget>
    <AssemblyTitle>Optimized MongoDB Process Monitor</AssemblyTitle>
    <AssemblyDescription>Enhanced MongoDB monitoring with stability focus</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>

  </PropertyGroup>

  <ItemGroup>
    <None Remove="Worker.cs~RF2fb410c1.TMP" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="3.1.32" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="3.1.32" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="3.1.32" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="3.1.32" />
    <PackageReference Include="MongoDB.Driver" Version="2.19.2" />
    <PackageReference Include="Serilog" Version="2.12.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="sample-mongod.cfg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="mongod-optimized.cfg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="README.md">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
