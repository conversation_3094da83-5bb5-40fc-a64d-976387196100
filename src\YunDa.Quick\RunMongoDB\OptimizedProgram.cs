using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.IO;
using System.Runtime;
using System.Text;
using System.Threading.Tasks;

namespace RunMongoDB
{
    /// <summary>
    /// Optimized program entry point for MongoDB process monitoring
    /// Focuses on stability, crash recovery, and performance optimization
    /// </summary>
    public class OptimizedProgram
    {
        public static async Task Main(string[] args)
        {
            // Set console encoding to support Chinese characters
            Console.OutputEncoding = Encoding.UTF8;
            Console.InputEncoding = Encoding.UTF8;

            // Check for command line arguments
            if (args.Length > 0)
            {
                switch (args[0].ToLower())
                {
                    case "diag":
                        MongoDBDiagnostics.RunDiagnostics();
                        return;
                    case "test":
                        await RunTestModeAsync();
                        return;
                    case "startup-test":
                        await RunStartupReliabilityTestAsync();
                        return;
                    case "help":
                    case "--help":
                    case "-h":
                        ShowHelp();
                        return;
                }
            }

            try
            {
                // Load configuration
                var configuration = LoadConfiguration();
                var mongoDBSettings = configuration.GetSection("MongoDBSetting").Get<MongoDBSettings>();

                // Configure logging
                ConfigureSerilog(mongoDBSettings);

                // Display startup information
                DisplayStartupInfo(mongoDBSettings);

                // Configure runtime for optimal performance
                ConfigureRuntime();

                // Start the optimized worker
                Log.Information("Initializing Optimized MongoDB Worker...");
                var worker = new OptimizedMongoDBWorker(mongoDBSettings);

                Log.Information("Starting optimized MongoDB monitoring and recovery service...");
                Log.Information("Press Ctrl+C to stop the application");

                // Handle graceful shutdown
                Console.CancelKeyPress += (sender, e) => {
                    e.Cancel = true;
                    Log.Information("Shutdown signal received. Stopping MongoDB monitor...");
                    worker.Stop();
                    Environment.Exit(0);
                };

                // Execute the optimized worker
                await worker.ExecuteAsync();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Critical error in optimized MongoDB monitor");
                Console.WriteLine($"Fatal error: {ex.Message}");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
                Environment.Exit(1);
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        /// <summary>
        /// Loads application configuration
        /// </summary>
        private static IConfiguration LoadConfiguration()
        {
            var configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");

            if (!File.Exists(configPath))
            {
                throw new FileNotFoundException($"Configuration file not found: {configPath}");
            }

            return new ConfigurationBuilder()
                .AddJsonFile(configPath, optional: false, reloadOnChange: true)
                .AddJsonFile("appsettings.Development.json", optional: true)
                .AddEnvironmentVariables()
                .Build();
        }

        /// <summary>
        /// Configures Serilog for optimized logging
        /// </summary>
        private static void ConfigureSerilog(MongoDBSettings settings)
        {
            var logPath = Path.Combine(settings.LogDirectory, "OptimizedRunMongoDB-.txt");

            // Ensure log directory exists
            Directory.CreateDirectory(settings.LogDirectory);

            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Information()
                .WriteTo.Console(
                    outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
                .WriteTo.File(
                    path: logPath,
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 7,
                    outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}",
                    encoding: Encoding.UTF8,
                    buffered: true) // Enable buffering for better performance
                .CreateLogger();
        }

        /// <summary>
        /// Displays startup information and warnings
        /// </summary>
        private static void DisplayStartupInfo(MongoDBSettings settings)
        {
            Log.Information("=== Optimized MongoDB Monitor Console Application Started ===");
            Log.Information("Application Version: 2.0.0 (Optimized for Stability)");
            Log.Information("Current Directory: {CurrentDirectory}", Directory.GetCurrentDirectory());
            
            // Display runtime configuration
            var isServerGC = GCSettings.IsServerGC;
            Log.Information("Server GC Enabled: {IsServerGC}", isServerGC);
            Log.Information("GC Latency Mode: {LatencyMode}", GCSettings.LatencyMode);
            
            // Display MongoDB configuration
            LogMongoDBConfiguration(settings);
            
            // Display optimization warnings
            DisplayOptimizationWarnings(settings);
        }

        /// <summary>
        /// Logs MongoDB configuration details
        /// </summary>
        private static void LogMongoDBConfiguration(MongoDBSettings settings)
        {
            Log.Information("MongoDB Configuration (Optimized):");
            Log.Information("  Host: {Host}:{Port}", settings.Host, settings.Port);
            Log.Information("  Database: {DatabaseName}", settings.DatabaseName);
            Log.Information("  Authentication: {IsAuth}", settings.IsAuth);
            Log.Information("  Data Directory: {DataDirectory}", settings.DataDirectory);
            Log.Information("  Log Directory: {LogDirectory}", settings.LogDirectory);
            Log.Information("  Process Check Interval: {ProcessCheckInterval}s", settings.ProcessCheckIntervalSeconds);
            Log.Information("  System Hang Detection: {SystemHangDetection}s", settings.SystemHangDetectionSeconds);
            Log.Information("  Memory Optimization Interval: {MemoryOptimization}min", settings.MemoryOptimizationIntervalMinutes);
            Log.Information("  Max Restart Attempts: {MaxRestartAttempts}", settings.MaxRestartAttempts);
            Log.Information("  Target Memory Usage: {TargetMemory}MB", settings.TargetMemoryUsageMB);
        }

        /// <summary>
        /// Displays optimization warnings
        /// </summary>
        private static void DisplayOptimizationWarnings(MongoDBSettings settings)
        {
            Log.Warning("=== OPTIMIZATION WARNINGS ===");
            Log.Warning("This application is optimized for STABILITY over DATA PROTECTION");
            
            if (!settings.EnableJournaling)
            {
                Log.Warning("WARNING: MongoDB journaling is DISABLED - reduced crash recovery");
            }
            
            if (!settings.EnableDataIntegrityChecks)
            {
                Log.Warning("WARNING: Data integrity checks are DISABLED - reduced data safety");
            }
            
            if (!settings.EnableDataValidation)
            {
                Log.Warning("WARNING: Data validation is DISABLED - reduced data safety");
            }
            
            if (!settings.EnableAutoBackup)
            {
                Log.Warning("WARNING: Automatic backups are DISABLED - no automatic data backup");
            }
            
            Log.Warning("These settings prioritize MongoDB runtime stability and performance");
            Log.Warning("Use only in environments where data loss risk is acceptable");
            Log.Warning("=== END WARNINGS ===");
        }

        /// <summary>
        /// Configures .NET runtime for optimal performance
        /// </summary>
        private static void ConfigureRuntime()
        {
            try
            {
                // Configure garbage collection for better performance
                if (GCSettings.IsServerGC)
                {
                    GCSettings.LatencyMode = GCLatencyMode.SustainedLowLatency;
                    Log.Information("Configured GC for sustained low latency");
                }
                
                // Set process priority to high for better responsiveness
                System.Diagnostics.Process.GetCurrentProcess().PriorityClass = 
                    System.Diagnostics.ProcessPriorityClass.High;
                Log.Information("Set process priority to High for better responsiveness");
            }
            catch (Exception ex)
            {
                Log.Warning("Could not configure runtime optimizations: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Runs test mode for validation
        /// </summary>
        private static async Task RunTestModeAsync()
        {
            Console.WriteLine("=== MongoDB Monitor Test Mode ===");

            try
            {
                var configuration = LoadConfiguration();
                var settings = configuration.GetSection("MongoDBSetting").Get<MongoDBSettings>();

                ConfigureSerilog(settings);

                Log.Information("Running MongoDB Monitor tests...");

                // Test configuration loading
                Log.Information("✓ Configuration loaded successfully");

                // Test MongoDB process detection
                var processManager = new Services.MongoDBProcessManager(settings);
                var isRunning = processManager.IsMongoDBRunning();
                Log.Information("MongoDB process running: {IsRunning}", isRunning);

                // Test system recovery manager
                var systemRecovery = new Services.SystemRecoveryManager(settings, processManager);
                var isResponsive = await systemRecovery.CheckSystemResponsivenessAsync();
                Log.Information("System responsiveness: {IsResponsive}", isResponsive);

                // Test memory optimizer
                var memoryOptimizer = new Services.AdvancedMemoryOptimizer(settings);
                var shouldOptimize = memoryOptimizer.ShouldOptimizeMemory();
                Log.Information("Memory optimization needed: {ShouldOptimize}", shouldOptimize);

                // Test aggressive startup manager
                var configService = new Services.MongoDBConfigurationService(settings);
                var pathConfig = configService.ResolveConfigurationPaths();
                var aggressiveStartup = new Services.AggressiveStartupManager(settings, processManager, pathConfig);
                Log.Information("✓ Aggressive startup manager initialized");

                Log.Information("✓ All tests completed successfully");
                Console.WriteLine("Test mode completed. Press any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Log.Error("Test mode failed: {Error}", ex.Message);
                Console.WriteLine($"Test failed: {ex.Message}");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// Runs startup reliability tests
        /// </summary>
        private static async Task RunStartupReliabilityTestAsync()
        {
            Console.WriteLine("=== MongoDB Startup Reliability Test ===");
            Console.WriteLine("WARNING: This test will stop and restart MongoDB multiple times");
            Console.WriteLine("Press 'Y' to continue or any other key to cancel...");

            var key = Console.ReadKey();
            Console.WriteLine();

            if (key.Key != ConsoleKey.Y)
            {
                Console.WriteLine("Startup reliability test cancelled.");
                return;
            }

            try
            {
                var configuration = LoadConfiguration();
                var settings = configuration.GetSection("MongoDBSetting").Get<MongoDBSettings>();

                ConfigureSerilog(settings);

                Log.Information("Starting MongoDB startup reliability tests...");

                // Initialize services
                var processManager = new Services.MongoDBProcessManager(settings);
                var configService = new Services.MongoDBConfigurationService(settings);
                var pathConfig = configService.ResolveConfigurationPaths();
                var aggressiveStartup = new Services.AggressiveStartupManager(settings, processManager, pathConfig);
                var reliabilityTester = new Services.StartupReliabilityTester(settings, aggressiveStartup, pathConfig);

                // Run tests
                var results = await reliabilityTester.RunStartupReliabilityTestsAsync();

                // Display results
                Console.WriteLine();
                Console.WriteLine("=== TEST RESULTS ===");
                Console.WriteLine($"Overall Success: {(results.OverallSuccess ? "PASS" : "FAIL")}");
                Console.WriteLine($"Total Duration: {results.TotalTestDuration.TotalSeconds:F1} seconds");
                Console.WriteLine();

                Console.WriteLine("Individual Test Results:");
                DisplayTestResult("Normal Startup", results.NormalStartupTest);
                DisplayTestResult("Lock File Recovery", results.LockFileRecoveryTest);
                DisplayTestResult("Diagnostic Data Recovery", results.DiagnosticDataRecoveryTest);
                DisplayTestResult("Journal Corruption Recovery", results.JournalCorruptionRecoveryTest);
                DisplayTestResult("Emergency Startup", results.EmergencyStartupTest);

                if (results.NuclearOptionTest != null)
                {
                    DisplayTestResult("Nuclear Option", results.NuclearOptionTest);
                }

                Console.WriteLine();
                Console.WriteLine("Startup reliability test completed. Press any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Log.Error("Startup reliability test failed: {Error}", ex.Message);
                Console.WriteLine($"Test failed: {ex.Message}");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// Display individual test result
        /// </summary>
        private static void DisplayTestResult(string testName, Services.TestResult result)
        {
            if (result != null)
            {
                var status = result.Success ? "PASS" : "FAIL";
                Console.WriteLine($"  {testName}: {status} ({result.Duration.TotalSeconds:F1}s, {result.StartupAttempts} attempts)");
                if (!string.IsNullOrEmpty(result.Details))
                {
                    Console.WriteLine($"    {result.Details}");
                }
            }
        }

        /// <summary>
        /// Shows help information
        /// </summary>
        private static void ShowHelp()
        {
            Console.WriteLine("=== Optimized MongoDB Monitor Help ===");
            Console.WriteLine();
            Console.WriteLine("Usage: OptimizedRunMongoDB [command]");
            Console.WriteLine();
            Console.WriteLine("Commands:");
            Console.WriteLine("  (no args)     - Start the optimized MongoDB monitor");
            Console.WriteLine("  test          - Run test mode to validate configuration");
            Console.WriteLine("  startup-test  - Run startup reliability tests (WARNING: Stops/starts MongoDB)");
            Console.WriteLine("  diag          - Run diagnostics to check MongoDB setup");
            Console.WriteLine("  help          - Show this help information");
            Console.WriteLine();
            Console.WriteLine("Features:");
            Console.WriteLine("  • Enhanced process monitoring with deadlock detection");
            Console.WriteLine("  • System hang detection and recovery");
            Console.WriteLine("  • Advanced memory optimization");
            Console.WriteLine("  • Automatic crash recovery");
            Console.WriteLine("  • Performance-optimized configuration");
            Console.WriteLine("  • Aggressive startup reliability with escalating recovery");
            Console.WriteLine("  • Automatic lock file and corruption cleanup");
            Console.WriteLine("  • Emergency and nuclear startup options");
            Console.WriteLine();
            Console.WriteLine("WARNING: This version prioritizes stability over data protection");
            Console.WriteLine("Data integrity features are disabled for maximum performance");
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
