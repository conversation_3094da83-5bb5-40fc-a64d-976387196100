{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "MongoDBSetting": {
    "Host": "127.0.0.1",
    "Port": "37017",
    "DatabaseName": "soms_mongodb",
    "IsAuth": "false",
    "UserName": "isasAdmin",
    "PassWord": "123456",
    "MongoDBExecutablePath": "D:\\SOMS\\大数据库\\mongod.exe",
    "MongoDBConfigPath": "D:\\SOMS\\大数据库\\mongod.cfg",
    "DataDirectory": "D:\\SOMS\\Data\\MongoDB\\data\\",
    "LogDirectory": "D:\\SOMS\\Logs\\",
    "BackupDirectory": "D:\\SOMS\\Backup\\MongoDB\\",

    // Enhanced monitoring settings (optimized for stability)
    "HealthCheckIntervalSeconds": 15,
    "ProcessCheckIntervalSeconds": 2,
    "MaxRestartAttempts": 5,
    "RestartDelaySeconds": 5,
    "ConnectionTimeoutSeconds": 15,
    "MaxConnectionPoolSize": 200,
    "MinConnectionPoolSize": 10,

    // Performance thresholds (optimized for stability)
    "MaxMemoryUsageMB": 6144,
    "MaxActiveConnections": 150,
    "MaxCpuUsagePercent": 90.0,

    // Stability-focused settings (disable data protection for performance)
    "EnableAutoBackup": false,
    "BackupRetentionDays": 3,
    "EnablePerformanceOptimization": true,
    "OptimizationIntervalHours": 12,
    "EnableDataIntegrityChecks": false,
    "EnableJournaling": false,
    "EnableDataValidation": false,

    // Advanced monitoring settings
    "SystemHangDetectionSeconds": 60,
    "ProcessResponseTimeoutSeconds": 30,
    "MemoryOptimizationIntervalMinutes": 30,
    "EnableAdvancedProcessMonitoring": true,
    "EnableSystemRecovery": true,
    "MaxSystemHangRecoveryAttempts": 3,
    "EnableEmergencyRestart": true,

    // Memory optimization settings
    "TargetMemoryUsageMB": 4096,
    "EnableMemoryCompaction": true,
    "EnableGarbageCollectionOptimization": true,
    "MemoryPressureThresholdPercent": 80,

    // Alert settings
    "EnableEmailAlerts": false,
    "AlertEmailRecipients": "",
    "SmtpServer": "",
    "SmtpPort": 587,
    "SmtpUsername": "",
    "SmtpPassword": ""
  }
}
