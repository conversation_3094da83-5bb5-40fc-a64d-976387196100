using System;
using System.Threading;
using System.Threading.Tasks;
using Serilog;
using RunMongoDB.Services;

namespace RunMongoDB
{
    /// <summary>
    /// Optimized MongoDB worker focused on stability, crash recovery, and performance
    /// Prioritizes MongoDB runtime stability over data integrity checks
    /// </summary>
    public class OptimizedMongoDBWorker
    {
        private readonly MongoDBSettings _settings;
        private readonly MongoDBProcessManager _processManager;
        private readonly SystemRecoveryManager _systemRecovery;
        private readonly AdvancedMemoryOptimizer _memoryOptimizer;
        private readonly EnhancedProcessMonitor _processMonitor;
        private readonly MongoDBHealthMonitor _healthMonitor;
        
        private CancellationTokenSource _cancellationTokenSource;
        private bool _isRunning = false;
        private DateTime _lastSystemCheck = DateTime.MinValue;
        private DateTime _lastMemoryOptimization = DateTime.MinValue;
        private int _consecutiveFailures = 0;

        public OptimizedMongoDBWorker(MongoDBSettings settings)
        {
            _settings = settings;
            _cancellationTokenSource = new CancellationTokenSource();

            // Initialize all services
            _processManager = new MongoDBProcessManager(_settings);
            _systemRecovery = new SystemRecoveryManager(_settings, _processManager);
            _memoryOptimizer = new AdvancedMemoryOptimizer(_settings);
            _processMonitor = new EnhancedProcessMonitor(_settings);
            _healthMonitor = new MongoDBHealthMonitor(_settings);

            Log.Information("Optimized MongoDB Worker initialized with enhanced monitoring and recovery");
            Log.Warning("WARNING: This worker prioritizes stability over data protection");
        }

        /// <summary>
        /// Starts the optimized MongoDB monitoring and management service
        /// </summary>
        public async Task ExecuteAsync()
        {
            _isRunning = true;
            Log.Information("Starting optimized MongoDB monitoring service");

            try
            {
                // Initial setup and MongoDB startup
                await InitialSetupAsync();

                // Start main monitoring loop
                await OptimizedMonitoringLoopAsync(_cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                Log.Fatal("Critical error in optimized MongoDB worker: {Error}", ex.Message);
                throw;
            }
            finally
            {
                _isRunning = false;
                Log.Information("Optimized MongoDB Worker stopped");
            }
        }

        /// <summary>
        /// Performs initial setup with focus on stability
        /// </summary>
        private async Task InitialSetupAsync()
        {
            try
            {
                Log.Information("Performing initial MongoDB setup with stability optimizations");

                // Ensure MongoDB is running
                if (!_processManager.IsMongoDBRunning())
                {
                    Log.Information("MongoDB not running. Starting with optimized configuration...");
                    var started = await _processManager.StartMongoDBAsync();
                    
                    if (!started)
                    {
                        Log.Error("Failed to start MongoDB during initial setup");
                        
                        // Try emergency recovery
                        if (_settings.EnableEmergencyRestart)
                        {
                            Log.Warning("Attempting emergency MongoDB startup");
                            await EmergencyStartupAsync();
                        }
                        else
                        {
                            throw new Exception("Failed to start MongoDB and emergency restart is disabled");
                        }
                    }
                }

                // Wait for MongoDB to be ready
                await Task.Delay(5000);

                // Perform initial memory optimization
                if (_settings.EnableMemoryCompaction)
                {
                    Log.Information("Performing initial memory optimization");
                    await _memoryOptimizer.OptimizeMemoryAsync();
                }

                Log.Information("Initial MongoDB setup completed successfully");
            }
            catch (Exception ex)
            {
                Log.Error("Error during initial MongoDB setup: {Error}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Main optimized monitoring loop with enhanced detection and recovery
        /// </summary>
        private async Task OptimizedMonitoringLoopAsync(CancellationToken cancellationToken)
        {
            Log.Information("Starting optimized MongoDB monitoring loop");
            Log.Information("Process check interval: {ProcessInterval}s, System check interval: {SystemInterval}s", 
                _settings.ProcessCheckIntervalSeconds, _settings.SystemHangDetectionSeconds);

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // 1. Enhanced process monitoring
                    await PerformEnhancedProcessMonitoringAsync();

                    // 2. System responsiveness check
                    await PerformSystemResponsivenessCheckAsync();

                    // 3. Memory optimization (if needed)
                    await PerformMemoryOptimizationAsync();

                    // 4. Health monitoring (lightweight)
                    await PerformLightweightHealthCheckAsync();

                    // Reset consecutive failures on successful cycle
                    _consecutiveFailures = 0;

                    // Wait before next check
                    await Task.Delay(_settings.ProcessCheckIntervalSeconds * 1000, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    Log.Information("Optimized MongoDB monitoring loop cancelled");
                    break;
                }
                catch (Exception ex)
                {
                    _consecutiveFailures++;
                    Log.Error("Error in optimized monitoring loop (failure #{FailureCount}): {Error}", 
                        _consecutiveFailures, ex.Message);
                    
                    // If too many consecutive failures, attempt recovery
                    if (_consecutiveFailures >= 3)
                    {
                        Log.Warning("Multiple consecutive monitoring failures detected. Attempting recovery...");
                        await HandleMonitoringFailuresAsync();
                        _consecutiveFailures = 0;
                    }
                    
                    // Wait longer on error to avoid rapid error loops
                    await Task.Delay(10000, cancellationToken);
                }
            }
        }

        /// <summary>
        /// Enhanced process monitoring with deadlock and hang detection
        /// </summary>
        private async Task PerformEnhancedProcessMonitoringAsync()
        {
            try
            {
                var monitoringResult = await _processMonitor.MonitorMongoDBProcessesAsync();
                
                if (!monitoringResult.IsHealthy)
                {
                    Log.Warning("Enhanced process monitoring detected issues: {Issues}", 
                        string.Join(", ", monitoringResult.Issues));
                    
                    // Handle process issues immediately
                    await HandleProcessIssuesAsync(monitoringResult);
                }
                else if (monitoringResult.ProcessCount == 0)
                {
                    Log.Warning("No MongoDB processes detected. Attempting restart...");
                    await HandleMissingProcessAsync();
                }
                else
                {
                    Log.Debug("Enhanced process monitoring - all {ProcessCount} processes healthy", 
                        monitoringResult.ProcessCount);
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error in enhanced process monitoring: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// System responsiveness monitoring with hang detection
        /// </summary>
        private async Task PerformSystemResponsivenessCheckAsync()
        {
            try
            {
                var timeSinceLastCheck = DateTime.Now - _lastSystemCheck;
                
                if (timeSinceLastCheck.TotalSeconds >= _settings.SystemHangDetectionSeconds)
                {
                    var isResponsive = await _systemRecovery.CheckSystemResponsivenessAsync();
                    
                    if (!isResponsive)
                    {
                        Log.Warning("System responsiveness check failed - potential system hang detected");
                        // System recovery is handled internally by SystemRecoveryManager
                    }
                    else
                    {
                        Log.Debug("System responsiveness check passed");
                    }
                    
                    _lastSystemCheck = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error in system responsiveness check: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Memory optimization with pressure monitoring
        /// </summary>
        private async Task PerformMemoryOptimizationAsync()
        {
            try
            {
                var timeSinceLastOptimization = DateTime.Now - _lastMemoryOptimization;
                
                if (_memoryOptimizer.ShouldOptimizeMemory() || 
                    timeSinceLastOptimization.TotalMinutes >= _settings.MemoryOptimizationIntervalMinutes)
                {
                    Log.Information("Performing memory optimization");
                    var optimized = await _memoryOptimizer.OptimizeMemoryAsync();
                    
                    if (optimized)
                    {
                        _lastMemoryOptimization = DateTime.Now;
                        Log.Information("Memory optimization completed successfully");
                    }
                    else
                    {
                        Log.Warning("Memory optimization encountered issues");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error in memory optimization: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Lightweight health check focused on critical metrics only
        /// </summary>
        private async Task PerformLightweightHealthCheckAsync()
        {
            try
            {
                // Only perform basic connectivity check to avoid overhead
                var isResponsive = await _systemRecovery.CheckMongoDBProcessResponsivenessAsync();
                
                if (!isResponsive)
                {
                    Log.Warning("MongoDB process responsiveness check failed");
                    await HandleUnresponsiveProcessAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Debug("Error in lightweight health check: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Handles process issues detected by enhanced monitoring
        /// </summary>
        private async Task HandleProcessIssuesAsync(ProcessMonitoringResult result)
        {
            try
            {
                foreach (var issue in result.Issues)
                {
                    if (issue.Contains("not responding") || issue.Contains("deadlock"))
                    {
                        Log.Warning("Critical process issue detected: {Issue}", issue);
                        await _processManager.RestartMongoDBAsync();
                        break;
                    }
                    else if (issue.Contains("memory usage too high"))
                    {
                        Log.Information("High memory usage detected - performing emergency optimization");
                        await _memoryOptimizer.OptimizeMemoryAsync();
                    }
                    else if (issue.Contains("CPU usage too high"))
                    {
                        Log.Information("High CPU usage detected - checking for system hang");
                        await _systemRecovery.CheckSystemResponsivenessAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error handling process issues: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Handles missing MongoDB process
        /// </summary>
        private async Task HandleMissingProcessAsync()
        {
            try
            {
                Log.Warning("MongoDB process missing - attempting restart");
                var restarted = await _processManager.StartMongoDBAsync();
                
                if (!restarted)
                {
                    Log.Error("Failed to restart missing MongoDB process");
                    
                    if (_settings.EnableEmergencyRestart)
                    {
                        await EmergencyStartupAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error handling missing process: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Handles unresponsive MongoDB process
        /// </summary>
        private async Task HandleUnresponsiveProcessAsync()
        {
            try
            {
                Log.Warning("MongoDB process unresponsive - attempting recovery");
                
                // Try graceful restart first
                var restarted = await _processManager.RestartMongoDBAsync();
                
                if (!restarted)
                {
                    Log.Error("Graceful restart failed - attempting emergency restart");
                    await EmergencyStartupAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error handling unresponsive process: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Emergency startup procedure with minimal safety checks
        /// </summary>
        private async Task EmergencyStartupAsync()
        {
            try
            {
                Log.Warning("Performing emergency MongoDB startup");
                
                // Force stop all MongoDB processes
                await _processManager.StopMongoDBAsync(graceful: false);
                
                // Wait for cleanup
                await Task.Delay(3000);
                
                // Force memory cleanup
                await _memoryOptimizer.OptimizeMemoryAsync();
                
                // Start MongoDB
                var started = await _processManager.StartMongoDBAsync();
                
                if (started)
                {
                    Log.Information("Emergency MongoDB startup successful");
                }
                else
                {
                    Log.Error("Emergency MongoDB startup failed");
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error during emergency startup: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Handles multiple consecutive monitoring failures
        /// </summary>
        private async Task HandleMonitoringFailuresAsync()
        {
            try
            {
                Log.Warning("Handling multiple consecutive monitoring failures");
                
                // Perform system recovery
                await _systemRecovery.CheckSystemResponsivenessAsync();
                
                // Force memory optimization
                await _memoryOptimizer.OptimizeMemoryAsync();
                
                // Check if MongoDB is still running
                if (!_processManager.IsMongoDBRunning())
                {
                    await EmergencyStartupAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error handling monitoring failures: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Stops the optimized MongoDB worker
        /// </summary>
        public void Stop()
        {
            Log.Information("Stopping Optimized MongoDB Worker...");
            _cancellationTokenSource?.Cancel();
        }

        /// <summary>
        /// Disposes resources
        /// </summary>
        public void Dispose()
        {
            _cancellationTokenSource?.Dispose();
            _processManager?.Dispose();
        }

        public bool IsRunning => _isRunning;
        public int ConsecutiveFailures => _consecutiveFailures;
        public DateTime LastSystemCheck => _lastSystemCheck;
        public DateTime LastMemoryOptimization => _lastMemoryOptimization;
    }
}
