using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Management;
using System.Linq;
using Serilog;

namespace RunMongoDB.Services
{
    /// <summary>
    /// Advanced system recovery manager for detecting and recovering from system hangs,
    /// process deadlocks, and system unresponsiveness
    /// </summary>
    public class SystemRecoveryManager
    {
        private readonly MongoDBSettings _settings;
        private readonly MongoDBProcessManager _processManager;
        private DateTime _lastSystemResponseTime = DateTime.Now;
        private DateTime _lastProcessResponseTime = DateTime.Now;
        private int _systemHangRecoveryAttempts = 0;
        private bool _isRecoveryInProgress = false;

        public SystemRecoveryManager(MongoDBSettings settings, MongoDBProcessManager processManager)
        {
            _settings = settings;
            _processManager = processManager;
            Log.Information("System Recovery Manager initialized with advanced monitoring capabilities");
        }

        /// <summary>
        /// Monitors system responsiveness and detects hangs
        /// </summary>
        public async Task<bool> CheckSystemResponsivenessAsync()
        {
            try
            {
                var startTime = DateTime.Now;
                
                // Test system responsiveness by checking process list
                var isResponsive = await TestSystemResponsivenessAsync();
                
                var responseTime = DateTime.Now - startTime;
                
                if (isResponsive && responseTime.TotalSeconds < _settings.SystemHangDetectionSeconds)
                {
                    _lastSystemResponseTime = DateTime.Now;
                    _systemHangRecoveryAttempts = 0; // Reset recovery attempts
                    return true;
                }
                else
                {
                    var timeSinceLastResponse = DateTime.Now - _lastSystemResponseTime;
                    
                    if (timeSinceLastResponse.TotalSeconds > _settings.SystemHangDetectionSeconds)
                    {
                        Log.Warning("System hang detected - no response for {Seconds} seconds", 
                            timeSinceLastResponse.TotalSeconds);
                        
                        if (_settings.EnableSystemRecovery)
                        {
                            await HandleSystemHangAsync();
                        }
                        
                        return false;
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error checking system responsiveness: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Tests system responsiveness by performing lightweight operations
        /// </summary>
        private async Task<bool> TestSystemResponsivenessAsync()
        {
            try
            {
                // Test 1: Process enumeration
                var processes = Process.GetProcesses();
                if (processes.Length == 0)
                    return false;

                // Test 2: Memory status check
                var memoryStatus = await GetSystemMemoryStatusAsync();
                if (memoryStatus == null)
                    return false;

                // Test 3: MongoDB process specific check
                var mongoProcesses = Process.GetProcessesByName("mongod");
                if (mongoProcesses.Length > 0)
                {
                    foreach (var process in mongoProcesses)
                    {
                        try
                        {
                            // Check if process is responding
                            var isResponding = process.Responding;
                            if (!isResponding)
                            {
                                Log.Warning("MongoDB process {ProcessId} is not responding", process.Id);
                                return false;
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Error checking MongoDB process responsiveness: {Error}", ex.Message);
                            return false;
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error in system responsiveness test: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Handles system hang recovery procedures
        /// </summary>
        private async Task HandleSystemHangAsync()
        {
            if (_isRecoveryInProgress)
            {
                Log.Information("System recovery already in progress, skipping");
                return;
            }

            if (_systemHangRecoveryAttempts >= _settings.MaxSystemHangRecoveryAttempts)
            {
                Log.Error("Maximum system hang recovery attempts reached. Manual intervention required.");
                return;
            }

            _isRecoveryInProgress = true;
            _systemHangRecoveryAttempts++;

            try
            {
                Log.Warning("Initiating system hang recovery (attempt {Attempt}/{MaxAttempts})", 
                    _systemHangRecoveryAttempts, _settings.MaxSystemHangRecoveryAttempts);

                // Step 1: Force garbage collection to free memory
                await ForceGarbageCollectionAsync();

                // Step 2: Check and kill unresponsive MongoDB processes
                await KillUnresponsiveMongoProcessesAsync();

                // Step 3: Clear system caches if possible
                await ClearSystemCachesAsync();

                // Step 4: Restart MongoDB with emergency settings
                if (_settings.EnableEmergencyRestart)
                {
                    await EmergencyRestartMongoDBAsync();
                }

                // Step 5: Wait and verify recovery
                await Task.Delay(10000); // Wait 10 seconds for recovery

                var isRecovered = await TestSystemResponsivenessAsync();
                if (isRecovered)
                {
                    Log.Information("System hang recovery successful");
                    _lastSystemResponseTime = DateTime.Now;
                    _systemHangRecoveryAttempts = 0;
                }
                else
                {
                    Log.Warning("System hang recovery attempt failed");
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error during system hang recovery: {Error}", ex.Message);
            }
            finally
            {
                _isRecoveryInProgress = false;
            }
        }

        /// <summary>
        /// Forces garbage collection to free memory
        /// </summary>
        private async Task ForceGarbageCollectionAsync()
        {
            try
            {
                Log.Information("Forcing garbage collection to free memory");
                
                await Task.Run(() =>
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                });
                
                Log.Information("Garbage collection completed");
            }
            catch (Exception ex)
            {
                Log.Warning("Error during garbage collection: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Kills unresponsive MongoDB processes
        /// </summary>
        private async Task KillUnresponsiveMongoProcessesAsync()
        {
            try
            {
                var mongoProcesses = Process.GetProcessesByName("mongod");
                
                foreach (var process in mongoProcesses)
                {
                    try
                    {
                        if (!process.Responding)
                        {
                            Log.Warning("Killing unresponsive MongoDB process {ProcessId}", process.Id);
                            process.Kill(true);
                            await Task.Delay(2000); // Wait for process to die
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Warning("Error killing unresponsive process {ProcessId}: {Error}", 
                            process.Id, ex.Message);
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error killing unresponsive MongoDB processes: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Attempts to clear system caches
        /// </summary>
        private async Task ClearSystemCachesAsync()
        {
            try
            {
                Log.Information("Attempting to clear system caches");
                
                // This is a placeholder for system cache clearing
                // In a real implementation, you might use Windows API calls
                // or PowerShell commands to clear system caches
                
                await Task.Delay(1000); // Simulate cache clearing
                
                Log.Information("System cache clearing completed");
            }
            catch (Exception ex)
            {
                Log.Warning("Error clearing system caches: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Emergency restart of MongoDB with minimal settings
        /// </summary>
        private async Task EmergencyRestartMongoDBAsync()
        {
            try
            {
                Log.Warning("Performing emergency MongoDB restart");
                
                // Stop all MongoDB processes forcefully
                await _processManager.StopMongoDBAsync(graceful: false);
                
                // Wait a bit longer for cleanup
                await Task.Delay(5000);
                
                // Start MongoDB
                var started = await _processManager.StartMongoDBAsync();
                
                if (started)
                {
                    Log.Information("Emergency MongoDB restart successful");
                }
                else
                {
                    Log.Error("Emergency MongoDB restart failed");
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error during emergency MongoDB restart: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Gets system memory status
        /// </summary>
        private async Task<object> GetSystemMemoryStatusAsync()
        {
            try
            {
                return await Task.Run<object>(() =>
                {
                    try
                    {
                        using (var memoryCounter = new PerformanceCounter("Memory", "Available MBytes"))
                        {
                            var availableMemory = memoryCounter.NextValue();
                            return new { AvailableMemoryMB = availableMemory };
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Debug("Performance counter not available, using alternative method: {Error}", ex.Message);

                        // Alternative method using GC for memory estimation
                        var totalMemory = GC.GetTotalMemory(false) / 1024 / 1024; // Convert to MB
                        return new { AvailableMemoryMB = Math.Max(1024 - totalMemory, 512) }; // Estimate
                    }
                });
            }
            catch (Exception ex)
            {
                Log.Debug("Error getting system memory status: {Error}", ex.Message);
                return null;
            }
        }

        /// <summary>
        /// Checks if MongoDB process is responding to commands
        /// </summary>
        public async Task<bool> CheckMongoDBProcessResponsivenessAsync()
        {
            try
            {
                var mongoProcesses = Process.GetProcessesByName("mongod");
                
                if (mongoProcesses.Length == 0)
                {
                    Log.Debug("No MongoDB processes found for responsiveness check");
                    return false;
                }

                foreach (var process in mongoProcesses)
                {
                    try
                    {
                        // Check if process is responding
                        if (!process.Responding)
                        {
                            Log.Warning("MongoDB process {ProcessId} is not responding", process.Id);
                            return false;
                        }

                        // Check process CPU usage (high CPU might indicate hang)
                        var cpuUsage = await GetProcessCpuUsageAsync(process);
                        if (cpuUsage > 95.0) // Very high CPU usage
                        {
                            Log.Warning("MongoDB process {ProcessId} has very high CPU usage: {CpuUsage}%", 
                                process.Id, cpuUsage);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Warning("Error checking MongoDB process {ProcessId}: {Error}", 
                            process.Id, ex.Message);
                        return false;
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                _lastProcessResponseTime = DateTime.Now;
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error checking MongoDB process responsiveness: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Gets CPU usage for a specific process
        /// </summary>
        private async Task<double> GetProcessCpuUsageAsync(Process process)
        {
            try
            {
                return await Task.Run(() =>
                {
                    var startTime = DateTime.UtcNow;
                    var startCpuUsage = process.TotalProcessorTime;
                    
                    Thread.Sleep(500); // Wait 500ms
                    
                    var endTime = DateTime.UtcNow;
                    var endCpuUsage = process.TotalProcessorTime;
                    
                    var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
                    var totalMsPassed = (endTime - startTime).TotalMilliseconds;
                    var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);
                    
                    return cpuUsageTotal * 100;
                });
            }
            catch
            {
                return 0.0;
            }
        }

        public bool IsRecoveryInProgress => _isRecoveryInProgress;
        public int SystemHangRecoveryAttempts => _systemHangRecoveryAttempts;
        public DateTime LastSystemResponseTime => _lastSystemResponseTime;
        public DateTime LastProcessResponseTime => _lastProcessResponseTime;
    }
}
