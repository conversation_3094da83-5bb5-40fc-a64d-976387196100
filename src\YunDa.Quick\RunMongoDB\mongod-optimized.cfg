# MongoDB Configuration - Optimized for Stability and Performance
# WARNING: This configuration prioritizes performance over data safety
# Generated by RunMongoDB Enhanced Process Monitor
# Use only in environments where data loss risk is acceptable

# Storage settings - optimized for performance over safety
storage:
  dbPath: "D:/SOMS/Data/MongoDB/data/"
  journal:
    enabled: false  # Disabled for performance - reduces data safety
  engine: wiredTiger
  wiredTiger:
    engineConfig:
      cacheSizeGB: 4.0  # Adjust based on available memory
      statisticsLogDelaySecs: 0  # Disable statistics logging
      directoryForIndexes: false
    collectionConfig:
      blockCompressor: none  # Disable compression for speed
    indexConfig:
      prefixCompression: false  # Disable compression for speed

# Network settings - optimized for high throughput
net:
  port: 37017
  bindIp: 127.0.0.1
  maxIncomingConnections: 400  # High connection limit
  wireObjectCheck: false  # Disable for performance
  compression:
    compressors: disabled  # Disable compression for speed

# System log settings - minimal logging for performance
systemLog:
  destination: file
  path: "D:/SOMS/Logs/mongod.log"
  logAppend: true
  logRotate: reopen
  verbosity: 0  # Minimal logging for performance
  quiet: true  # Reduce log output

# Process management
processManagement:
  fork: false

# Operation profiling - disabled for performance
operationProfiling:
  mode: off  # Disabled for performance

# Security disabled for maximum performance
# WARNING: This is not suitable for production environments
security:
  authorization: disabled

# Replication settings (if using replica sets)
# replication:
#   replSetName: "rs0"

# Sharding settings (if using sharding)
# sharding:
#   clusterRole: configsvr

# Additional performance optimizations
setParameter:
  # Disable data validation for performance
  enableSchemaValidation: false
  
  # Reduce write concern timeout
  writeConcernMajorityJournalDefault: false
  
  # Optimize connection handling
  connPoolMaxShardedConnsPerHost: 200
  connPoolMaxConnsPerHost: 200
  
  # Disable slow operation logging
  slowOpThresholdMs: 10000
  
  # Optimize query execution
  internalQueryPlanEvaluationWorks: 10000
  internalQueryPlanEvaluationCollFraction: 0.3
  
  # Memory optimization
  wiredTigerConcurrentReadTransactions: 128
  wiredTigerConcurrentWriteTransactions: 128
  
  # Disable unnecessary features for performance
  enableTestCommands: false
  enableLocalhostAuthBypass: false
  
  # Optimize index builds
  maxIndexBuildMemoryUsageMegabytes: 500

# Comments explaining the optimizations:
#
# 1. Journaling Disabled:
#    - Significantly improves write performance
#    - Reduces disk I/O overhead
#    - WARNING: Increases risk of data loss on crash
#
# 2. Compression Disabled:
#    - Reduces CPU overhead for compression/decompression
#    - Increases disk space usage but improves performance
#
# 3. Minimal Logging:
#    - Reduces log file I/O overhead
#    - Makes troubleshooting more difficult
#
# 4. Wire Object Check Disabled:
#    - Skips validation of BSON objects
#    - Improves network performance
#    - WARNING: May allow corrupted data
#
# 5. Schema Validation Disabled:
#    - Skips document validation against schemas
#    - Improves insert/update performance
#    - WARNING: Allows invalid data to be stored
#
# 6. High Connection Limits:
#    - Allows more concurrent connections
#    - May increase memory usage
#
# 7. Optimized Cache Settings:
#    - Allocates more memory to WiredTiger cache
#    - Improves read performance
#    - Adjust cacheSizeGB based on available system memory
#
# IMPORTANT NOTES:
# - This configuration is optimized for maximum performance
# - Data safety features are intentionally disabled
# - Use only in development or non-critical environments
# - Regular backups are essential when using this configuration
# - Monitor system resources closely
# - Consider enabling journaling in production environments
